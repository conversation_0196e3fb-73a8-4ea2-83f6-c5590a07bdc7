#!/bin/bash

# Jarvis OS Build Script
# Builds the Jarvis OS project with all AI services and integrations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[BUILD]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Build configuration
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build"
OUT_DIR="$PROJECT_ROOT/out"
LOGS_DIR="$PROJECT_ROOT/logs"

# Build settings
BUILD_TYPE="${BUILD_TYPE:-debug}"
TARGET_DEVICE="${TARGET_DEVICE:-generic}"
PARALLEL_JOBS="${PARALLEL_JOBS:-$(nproc)}"

# Create necessary directories
create_directories() {
    print_status "Creating build directories..."
    
    mkdir -p "$BUILD_DIR"
    mkdir -p "$OUT_DIR"
    mkdir -p "$LOGS_DIR"
    
    print_success "Build directories created"
}

# Check build environment
check_environment() {
    print_status "Checking build environment..."
    
    # Check Java version
    if ! command -v java &> /dev/null; then
        print_error "Java not found. Please install OpenJDK 11."
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [[ "$JAVA_VERSION" -lt 11 ]]; then
        print_error "Java 11 or higher required. Found Java $JAVA_VERSION."
        exit 1
    fi
    
    # Check Android SDK
    if [[ -z "$ANDROID_HOME" ]]; then
        print_error "ANDROID_HOME not set. Please run setup-dev-env.sh first."
        exit 1
    fi
    
    # Check available disk space (need at least 10GB for build)
    AVAILABLE_SPACE=$(df "$OUT_DIR" | tail -1 | awk '{print $4}')
    REQUIRED_SPACE=10485760 # 10GB in KB
    
    if [[ $AVAILABLE_SPACE -lt $REQUIRED_SPACE ]]; then
        print_error "Insufficient disk space. Need at least 10GB free."
        exit 1
    fi
    
    print_success "Build environment check passed"
}

# Clean build artifacts
clean_build() {
    print_status "Cleaning previous build artifacts..."
    
    rm -rf "$OUT_DIR"/*
    rm -rf "$BUILD_DIR"/tmp/*
    
    print_success "Build artifacts cleaned"
}

# Compile Java sources
compile_java() {
    print_status "Compiling Java sources..."
    
    local log_file="$LOGS_DIR/java_compile.log"
    
    # Create classpath
    local classpath="$ANDROID_HOME/platforms/android-33/android.jar"

    # Add JUnit for tests if available
    if [[ -f "$PROJECT_ROOT/libs/junit-4.13.2.jar" ]]; then
        classpath="$classpath:$PROJECT_ROOT/libs/junit-4.13.2.jar"
    fi

    # Add Mockito for tests if available
    if [[ -f "$PROJECT_ROOT/libs/mockito-core-4.6.1.jar" ]]; then
        classpath="$classpath:$PROJECT_ROOT/libs/mockito-core-4.6.1.jar"
    fi

    # Find all Java source files
    local java_files=$(find "$PROJECT_ROOT" -name "*.java" -not -path "*/build/*" -not -path "*/out/*")
    
    if [[ -z "$java_files" ]]; then
        print_warning "No Java source files found"
        return 0
    fi
    
    # Compile Java sources
    mkdir -p "$OUT_DIR/classes"
    
    if javac -cp "$classpath" -d "$OUT_DIR/classes" $java_files > "$log_file" 2>&1; then
        print_success "Java compilation completed"
    else
        print_error "Java compilation failed. Check $log_file for details."
        tail -20 "$log_file"
        exit 1
    fi
}

# Create JAR files
create_jars() {
    print_status "Creating JAR files..."
    
    local classes_dir="$OUT_DIR/classes"
    local jars_dir="$OUT_DIR/jars"
    
    mkdir -p "$jars_dir"
    
    # Create core AI services JAR
    if [[ -d "$classes_dir/com/jarvis/core" ]]; then
        jar cf "$jars_dir/jarvis-core.jar" -C "$classes_dir" com/jarvis/core/
        print_success "Created jarvis-core.jar"
    fi
    
    # Create framework services JAR
    if [[ -d "$classes_dir/com/jarvis/framework" ]]; then
        jar cf "$jars_dir/jarvis-framework.jar" -C "$classes_dir" com/jarvis/framework/
        print_success "Created jarvis-framework.jar"
    fi
    
    # Create SystemUI JAR
    if [[ -d "$classes_dir/com/jarvis/systemui" ]]; then
        jar cf "$jars_dir/jarvis-systemui.jar" -C "$classes_dir" com/jarvis/systemui/
        print_success "Created jarvis-systemui.jar"
    fi
    
    # Create API JAR
    if [[ -d "$classes_dir/com/jarvis/api" ]]; then
        jar cf "$jars_dir/jarvis-api.jar" -C "$classes_dir" com/jarvis/api/
        print_success "Created jarvis-api.jar"
    fi
    
    # Create security JAR
    if [[ -d "$classes_dir/com/jarvis/security" ]]; then
        jar cf "$jars_dir/jarvis-security.jar" -C "$classes_dir" com/jarvis/security/
        print_success "Created jarvis-security.jar"
    fi
}

# Generate documentation
generate_docs() {
    print_status "Generating documentation..."
    
    local docs_dir="$OUT_DIR/docs"
    local log_file="$LOGS_DIR/javadoc.log"
    
    mkdir -p "$docs_dir"
    
    # Find all Java source files
    local java_files=$(find "$PROJECT_ROOT" -name "*.java" -not -path "*/build/*" -not -path "*/out/*")
    
    if [[ -n "$java_files" ]]; then
        if javadoc -d "$docs_dir" -cp "$ANDROID_HOME/platforms/android-33/android.jar" $java_files > "$log_file" 2>&1; then
            print_success "Documentation generated in $docs_dir"
        else
            print_warning "Documentation generation failed. Check $log_file for details."
        fi
    fi
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    local test_dir="$PROJECT_ROOT/tests"
    local log_file="$LOGS_DIR/tests.log"
    
    if [[ -d "$test_dir" ]]; then
        # Run unit tests (placeholder - would integrate with actual test framework)
        echo "Running unit tests..." > "$log_file"
        
        # Find test files
        local test_files=$(find "$test_dir" -name "*Test.java" 2>/dev/null || true)
        
        if [[ -n "$test_files" ]]; then
            print_status "Found test files: $(echo $test_files | wc -w)"
            # Test execution would go here
            print_success "Tests completed"
        else
            print_warning "No test files found"
        fi
    else
        print_warning "No tests directory found"
    fi
}

# Package build artifacts
package_build() {
    print_status "Packaging build artifacts..."
    
    local package_dir="$OUT_DIR/package"
    local version=$(grep "project.version" "$BUILD_DIR/build.properties" | cut -d'=' -f2 || echo "1.0.0-dev")
    local package_name="jarvis-os-$version-$BUILD_TYPE.tar.gz"
    
    mkdir -p "$package_dir"
    
    # Copy JAR files
    if [[ -d "$OUT_DIR/jars" ]]; then
        cp -r "$OUT_DIR/jars" "$package_dir/"
    fi
    
    # Copy documentation
    if [[ -d "$OUT_DIR/docs" ]]; then
        cp -r "$OUT_DIR/docs" "$package_dir/"
    fi
    
    # Copy configuration files
    cp -r "$PROJECT_ROOT/docs" "$package_dir/" 2>/dev/null || true
    cp "$PROJECT_ROOT/README.md" "$package_dir/" 2>/dev/null || true
    
    # Create package
    cd "$OUT_DIR"
    tar -czf "$package_name" -C package .
    
    print_success "Package created: $OUT_DIR/$package_name"
}

# Generate build report
generate_report() {
    print_status "Generating build report..."
    
    local report_file="$OUT_DIR/build-report.txt"
    
    cat > "$report_file" << EOF
Jarvis OS Build Report
=====================

Build Information:
- Build Type: $BUILD_TYPE
- Target Device: $TARGET_DEVICE
- Build Date: $(date)
- Build Host: $(hostname)
- Build User: $(whoami)

Build Statistics:
- Java Files: $(find "$PROJECT_ROOT" -name "*.java" -not -path "*/build/*" -not -path "*/out/*" | wc -l)
- JAR Files: $(find "$OUT_DIR/jars" -name "*.jar" 2>/dev/null | wc -l)
- Build Time: $((SECONDS / 60)) minutes $((SECONDS % 60)) seconds

Build Artifacts:
$(ls -la "$OUT_DIR" 2>/dev/null || echo "No artifacts found")

Build Logs:
$(ls -la "$LOGS_DIR" 2>/dev/null || echo "No logs found")
EOF
    
    print_success "Build report generated: $report_file"
}

# Main build function
main() {
    local start_time=$SECONDS
    
    echo "🤖 Jarvis OS Build System"
    echo "========================"
    echo "Build Type: $BUILD_TYPE"
    echo "Target Device: $TARGET_DEVICE"
    echo "Parallel Jobs: $PARALLEL_JOBS"
    echo ""
    
    # Build steps
    create_directories
    check_environment
    
    if [[ "$1" == "clean" ]]; then
        clean_build
        print_success "Clean completed"
        exit 0
    fi
    
    clean_build
    compile_java
    create_jars
    
    if [[ "$BUILD_TYPE" != "release" ]]; then
        generate_docs
        run_tests
    fi
    
    package_build
    generate_report
    
    local build_time=$((SECONDS - start_time))
    
    echo ""
    print_success "🎉 Build completed successfully!"
    echo "Build time: $((build_time / 60)) minutes $((build_time % 60)) seconds"
    echo "Artifacts: $OUT_DIR"
    echo ""
    echo "Next steps:"
    echo "1. Review build report: $OUT_DIR/build-report.txt"
    echo "2. Test the build: ./scripts/test.sh"
    echo "3. Deploy if ready: ./scripts/deploy.sh"
}

# Handle command line arguments
case "$1" in
    clean)
        main clean
        ;;
    release)
        BUILD_TYPE=release
        main
        ;;
    debug)
        BUILD_TYPE=debug
        main
        ;;
    *)
        main
        ;;
esac
