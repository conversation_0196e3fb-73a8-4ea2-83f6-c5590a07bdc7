package com.jarvis.demo;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.*;
import com.jarvis.core.ai.interfaces.*;
import com.jarvis.core.ai.services.*;
import com.jarvis.core.ai.ml.*;
import com.jarvis.core.ai.proactive.*;
import com.jarvis.framework.services.AiSystemServiceManager;
import com.jarvis.api.gemini.GeminiApiClient;
import com.jarvis.security.AiPrivacyManager;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Jarvis OS Demo Application
 * Demonstrates the core AI capabilities and services
 */
public class JarvisOsDemo extends Activity {
    
    private static final String TAG = "JarvisOsDemo";
    private static final String DEMO_USER_ID = "demo_user";
    
    // UI Components
    private TextView statusText;
    private EditText queryInput;
    private Button executeButton;
    private Button contextButton;
    private Button learningButton;
    private Button automationButton;
    private Button mlInsightsButton;
    private ListView resultsListView;
    private ProgressBar progressBar;
    
    // AI Services
    private AiSystemServiceManager aiServiceManager;
    private IAiContextEngine contextEngine;
    private IAiPlanningOrchestration planningService;
    private IAiPersonalization personalizationService;
    private GeminiApiClient geminiClient;
    private AiPrivacyManager privacyManager;

    // Phase 2 Enhanced Components
    private ContextFusionEngine fusionEngine;
    private ProactiveAutomationEngine automationEngine;
    private ContextPredictor contextPredictor;
    private AnomalyDetector anomalyDetector;
    private PatternMatcher patternMatcher;
    
    // Demo data
    private List<String> demoResults;
    private ArrayAdapter<String> resultsAdapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        Log.d(TAG, "Jarvis OS Demo starting...");
        
        initializeUI();
        initializeAiServices();
        setupDemoData();
        
        Log.d(TAG, "Jarvis OS Demo initialized");
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        cleanup();
    }
    
    /**
     * Initialize UI components
     */
    private void initializeUI() {
        // Create UI programmatically for demo purposes
        LinearLayout mainLayout = new LinearLayout(this);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(20, 20, 20, 20);
        
        // Title
        TextView titleText = new TextView(this);
        titleText.setText("🤖 Jarvis OS Demo");
        titleText.setTextSize(24);
        titleText.setPadding(0, 0, 0, 20);
        mainLayout.addView(titleText);
        
        // Status text
        statusText = new TextView(this);
        statusText.setText("Initializing AI services...");
        statusText.setPadding(0, 0, 0, 10);
        mainLayout.addView(statusText);
        
        // Progress bar
        progressBar = new ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
        progressBar.setVisibility(View.GONE);
        mainLayout.addView(progressBar);
        
        // Query input
        queryInput = new EditText(this);
        queryInput.setHint("Enter your request (e.g., 'open settings', 'what should I do?')");
        queryInput.setPadding(10, 10, 10, 10);
        mainLayout.addView(queryInput);
        
        // Buttons layout
        LinearLayout buttonsLayout = new LinearLayout(this);
        buttonsLayout.setOrientation(LinearLayout.HORIZONTAL);
        
        executeButton = new Button(this);
        executeButton.setText("Execute");
        executeButton.setOnClickListener(this::onExecuteClicked);
        buttonsLayout.addView(executeButton);
        
        contextButton = new Button(this);
        contextButton.setText("Show Context");
        contextButton.setOnClickListener(this::onContextClicked);
        buttonsLayout.addView(contextButton);
        
        learningButton = new Button(this);
        learningButton.setText("Learning Stats");
        learningButton.setOnClickListener(this::onLearningClicked);
        buttonsLayout.addView(learningButton);

        automationButton = new Button(this);
        automationButton.setText("Automation");
        automationButton.setOnClickListener(this::onAutomationClicked);
        buttonsLayout.addView(automationButton);

        mlInsightsButton = new Button(this);
        mlInsightsButton.setText("ML Insights");
        mlInsightsButton.setOnClickListener(this::onMlInsightsClicked);
        buttonsLayout.addView(mlInsightsButton);
        
        mainLayout.addView(buttonsLayout);
        
        // Results list
        TextView resultsLabel = new TextView(this);
        resultsLabel.setText("Results:");
        resultsLabel.setPadding(0, 20, 0, 10);
        mainLayout.addView(resultsLabel);
        
        resultsListView = new ListView(this);
        demoResults = new ArrayList<>();
        resultsAdapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1, demoResults);
        resultsListView.setAdapter(resultsAdapter);
        mainLayout.addView(resultsListView);
        
        setContentView(mainLayout);
    }
    
    /**
     * Initialize AI services
     */
    private void initializeAiServices() {
        try {
            // Initialize service manager
            aiServiceManager = AiSystemServiceManager.getInstance(this);
            
            // Initialize privacy manager
            privacyManager = AiPrivacyManager.getInstance(this);
            setupPrivacySettings();
            
            // Initialize Gemini API client
            geminiClient = new GeminiApiClient("demo-api-key", "gemini-pro");
            
            // Start AI services
            boolean servicesStarted = aiServiceManager.startAiServices();
            
            if (servicesStarted) {
                // Get service references
                contextEngine = aiServiceManager.getContextEngine();
                planningService = aiServiceManager.getPlanningOrchestration();
                personalizationService = aiServiceManager.getPersonalization();
                
                updateStatus("✅ AI services initialized successfully");
                enableUI(true);

                // Initialize Phase 2 enhanced components
                initializePhase2Components();

                // Initialize Gemini client
                geminiClient.initialize().thenAccept(success -> {
                    runOnUiThread(() -> {
                        if (success) {
                            updateStatus("✅ Phase 2 Enhanced AI - Jarvis OS is online!");
                        } else {
                            updateStatus("⚠️ Enhanced AI services ready (Gemini API offline)");
                        }
                    });
                });
                
            } else {
                updateStatus("❌ Failed to initialize AI services");
                enableUI(false);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing AI services", e);
            updateStatus("❌ Error: " + e.getMessage());
            enableUI(false);
        }
    }
    
    /**
     * Initialize Phase 2 enhanced components
     */
    private void initializePhase2Components() {
        try {
            // Initialize enhanced context fusion engine
            fusionEngine = new ContextFusionEngine();

            // Initialize ML components
            contextPredictor = new ContextPredictor();
            anomalyDetector = new AnomalyDetector();
            patternMatcher = new PatternMatcher();

            // Initialize proactive automation engine
            automationEngine = new ProactiveAutomationEngine(
                contextEngine, planningService, personalizationService);
            automationEngine.start();

            updateStatus("🚀 Phase 2 Enhanced AI components initialized");
            Log.d(TAG, "Phase 2 components initialized successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing Phase 2 components", e);
            updateStatus("⚠️ Phase 2 initialization failed: " + e.getMessage());
        }
    }

    /**
     * Setup privacy settings for demo
     */
    private void setupPrivacySettings() {
        // Grant permissions for demo components
        privacyManager.grantComponentPermission("demo_app", true);
        privacyManager.grantComponentPermission("context_engine", true);
        privacyManager.grantComponentPermission("planning_service", true);
        
        // Set privacy levels for demo (more permissive for demonstration)
        privacyManager.setPrivacyLevel(
            AiPrivacyManager.DataCategory.DEVICE_INFO, 
            AiPrivacyManager.PrivacyLevel.INTERNAL
        );
        privacyManager.setPrivacyLevel(
            AiPrivacyManager.DataCategory.APP_DATA, 
            AiPrivacyManager.PrivacyLevel.INTERNAL
        );
        privacyManager.setPrivacyLevel(
            AiPrivacyManager.DataCategory.BEHAVIORAL, 
            AiPrivacyManager.PrivacyLevel.INTERNAL
        );
        
        Log.d(TAG, "Privacy settings configured for demo");
    }
    
    /**
     * Setup demo data
     */
    private void setupDemoData() {
        // Add some demo context data
        if (contextEngine != null) {
            // Enable context collection for demo
            contextEngine.setContextEnabled(IAiContextEngine.ContextType.USER_ACTIVITY, true);
            contextEngine.setContextEnabled(IAiContextEngine.ContextType.APP_STATES, true);
            contextEngine.setContextEnabled(IAiContextEngine.ContextType.DEVICE_SENSORS, true);
            
            // Add sample context data
            Map<String, Object> activityData = new HashMap<>();
            activityData.put("currentApp", "com.jarvis.demo");
            activityData.put("screenState", "on");
            activityData.put("batteryLevel", 85);
            
            IAiContextEngine.ContextData contextData = new IAiContextEngine.ContextData(
                IAiContextEngine.ContextType.USER_ACTIVITY, activityData);
            contextEngine.updateContext(contextData);
        }
        
        // Add some demo preferences
        if (personalizationService != null) {
            IAiPersonalization.UserPreference pref1 = new IAiPersonalization.UserPreference(
                "preferred_launcher", 
                IAiPersonalization.PreferenceCategory.PRODUCTIVITY, 
                "com.android.launcher3"
            );
            pref1.confidence = 0.8f;
            personalizationService.updatePreference(DEMO_USER_ID, pref1);
            
            IAiPersonalization.UserPreference pref2 = new IAiPersonalization.UserPreference(
                "morning_routine", 
                IAiPersonalization.PreferenceCategory.BEHAVIORAL, 
                "check_calendar_and_weather"
            );
            pref2.confidence = 0.9f;
            personalizationService.updatePreference(DEMO_USER_ID, pref2);
            
            // Simulate some behavior learning
            Map<String, Object> behaviorContext = new HashMap<>();
            behaviorContext.put("timeOfDay", "morning");
            behaviorContext.put("dayOfWeek", "weekday");
            
            personalizationService.learnFromBehavior(DEMO_USER_ID, "app_opened", behaviorContext);
            personalizationService.learnFromBehavior(DEMO_USER_ID, "settings_accessed", behaviorContext);
        }
        
        Log.d(TAG, "Demo data setup completed");
    }
    
    /**
     * Handle execute button click
     */
    private void onExecuteClicked(View view) {
        String query = queryInput.getText().toString().trim();
        if (query.isEmpty()) {
            Toast.makeText(this, "Please enter a query", Toast.LENGTH_SHORT).show();
            return;
        }
        
        showProgress(true);
        addResult("🔄 Processing: " + query);
        
        // Get current context
        IAiContextEngine.FusedContext context = contextEngine != null ? 
            contextEngine.getCurrentContext() : new IAiContextEngine.FusedContext();
        
        // Create execution plan
        if (planningService != null) {
            try {
                IAiPlanningOrchestration.ExecutionPlan plan = planningService.createPlan(query, context);
                
                addResult("📋 Plan created with " + plan.actions.size() + " actions:");
                for (IAiPlanningOrchestration.PlanAction action : plan.actions) {
                    addResult("  • " + action.description + " (" + action.type + ")");
                }
                
                // Execute the plan
                IAiPlanningOrchestration.IExecutionCallback callback = new DemoExecutionCallback();
                planningService.executePlan(plan, callback);
                
            } catch (Exception e) {
                Log.e(TAG, "Error creating/executing plan", e);
                addResult("❌ Error: " + e.getMessage());
                showProgress(false);
            }
        } else {
            addResult("❌ Planning service not available");
            showProgress(false);
        }
        
        // Learn from this interaction
        if (personalizationService != null) {
            Map<String, Object> interactionContext = new HashMap<>();
            interactionContext.put("query", query);
            interactionContext.put("timestamp", System.currentTimeMillis());
            
            personalizationService.learnFromBehavior(DEMO_USER_ID, "query_executed", interactionContext);
        }
    }
    
    /**
     * Handle context button click
     */
    private void onContextClicked(View view) {
        if (contextEngine == null) {
            addResult("❌ Context engine not available");
            return;
        }
        
        addResult("📊 Current Context:");
        
        IAiContextEngine.FusedContext context = contextEngine.getCurrentContext();
        if (context != null) {
            addResult("  • Activity: " + context.currentActivity);
            addResult("  • Intent: " + context.userIntent);
            addResult("  • Urgency: " + context.urgencyLevel);
            addResult("  • Available Actions: " + context.availableActions.size());
            
            for (Map.Entry<IAiContextEngine.ContextType, IAiContextEngine.ContextData> entry : 
                 context.contexts.entrySet()) {
                addResult("  • " + entry.getKey() + ": " + entry.getValue().data.size() + " items");
            }
        } else {
            addResult("  No context data available");
        }
        
        // Show permissions
        addResult("🔒 Context Permissions:");
        Map<IAiContextEngine.ContextType, Boolean> permissions = contextEngine.getContextPermissions();
        for (Map.Entry<IAiContextEngine.ContextType, Boolean> entry : permissions.entrySet()) {
            String status = entry.getValue() ? "✅" : "❌";
            addResult("  " + status + " " + entry.getKey());
        }
    }
    
    /**
     * Handle learning button click
     */
    private void onLearningClicked(View view) {
        if (personalizationService == null) {
            addResult("❌ Personalization service not available");
            return;
        }
        
        addResult("🧠 Learning Statistics:");
        
        Map<String, Object> stats = personalizationService.getLearningStatistics(DEMO_USER_ID);
        for (Map.Entry<String, Object> entry : stats.entrySet()) {
            addResult("  • " + entry.getKey() + ": " + entry.getValue());
        }
        
        // Show behavioral patterns
        List<IAiPersonalization.BehavioralPattern> patterns = 
            personalizationService.getBehavioralPatterns(DEMO_USER_ID, 
                IAiPersonalization.ConfidenceLevel.LOW);
        
        addResult("🔄 Behavioral Patterns (" + patterns.size() + "):");
        for (IAiPersonalization.BehavioralPattern pattern : patterns) {
            addResult("  • " + pattern.description + " (confidence: " + pattern.confidence + ")");
        }
        
        // Show personalized suggestions
        Map<String, Object> currentContext = new HashMap<>();
        currentContext.put("timeOfDay", getCurrentTimeOfDay());
        
        List<String> suggestions = personalizationService.getPersonalizedSuggestions(
            DEMO_USER_ID, currentContext, 5);
        
        addResult("💡 Personalized Suggestions:");
        for (String suggestion : suggestions) {
            addResult("  • " + suggestion);
        }
    }

    /**
     * Handle automation button click
     */
    private void onAutomationClicked(View view) {
        if (automationEngine == null) {
            addResult("❌ Automation engine not available");
            return;
        }

        addResult("🤖 Proactive Automation:");

        // Get automation statistics
        ProactiveAutomationEngine.AutomationStatistics stats = automationEngine.getAutomationStatistics();
        addResult("  • Total Automations: " + stats.totalAutomations);
        addResult("  • Successful: " + stats.successfulAutomations);
        addResult("  • Success Rate: " + String.format("%.1f%%", stats.successRate * 100));
        addResult("  • Active: " + stats.activeAutomations);

        // Show automation suggestions
        List<ProactiveAutomationEngine.AutomationSuggestion> suggestions =
            automationEngine.getAutomationSuggestions();

        addResult("🔮 Automation Suggestions (" + suggestions.size() + "):");
        for (ProactiveAutomationEngine.AutomationSuggestion suggestion : suggestions) {
            String autoFlag = suggestion.autoExecute ? " [AUTO]" : "";
            addResult("  • " + suggestion.description +
                " (confidence: " + String.format("%.2f", suggestion.confidence) + ")" + autoFlag);

            // Show actions
            for (String action : suggestion.actions) {
                addResult("    - " + action);
            }
        }

        // Execute a high-confidence suggestion for demo
        if (!suggestions.isEmpty() && suggestions.get(0).confidence > 0.7f) {
            ProactiveAutomationEngine.AutomationSuggestion topSuggestion = suggestions.get(0);
            addResult("🚀 Auto-executing top suggestion: " + topSuggestion.description);

            automationEngine.executeAutomation(topSuggestion).thenAccept(result -> {
                runOnUiThread(() -> {
                    String status = result.success ? "✅" : "❌";
                    addResult(status + " Automation result: " + result.message);
                });
            });
        }
    }

    /**
     * Handle ML insights button click
     */
    private void onMlInsightsClicked(View view) {
        if (contextEngine == null) {
            addResult("❌ Context engine not available");
            return;
        }

        addResult("🧠 ML Insights & Analytics:");

        // Get current context with ML insights
        IAiContextEngine.FusedContext context = contextEngine.getCurrentContext();
        if (context != null) {
            addResult("  📊 Context Analysis:");
            addResult("    • Confidence Score: " + String.format("%.2f", context.confidenceScore));
            addResult("    • Context Types: " + context.contexts.size());

            // Show context patterns
            if (context.contextPatterns != null && !context.contextPatterns.isEmpty()) {
                addResult("  🔍 Detected Patterns (" + context.contextPatterns.size() + "):");
                for (Object patternObj : context.contextPatterns) {
                    if (patternObj instanceof ContextFusionEngine.ContextPattern) {
                        ContextFusionEngine.ContextPattern pattern =
                            (ContextFusionEngine.ContextPattern) patternObj;
                        addResult("    • " + pattern.description +
                            " (confidence: " + String.format("%.2f", pattern.confidence) + ")");
                    }
                }
            }

            // Show predictions
            if (context.predictedContext instanceof ContextFusionEngine.PredictedContext) {
                ContextFusionEngine.PredictedContext prediction =
                    (ContextFusionEngine.PredictedContext) context.predictedContext;
                addResult("  🔮 Predictions:");
                addResult("    • Confidence: " + String.format("%.2f", prediction.confidence));
                addResult("    • Timeframe: " + (prediction.predictionTimeframe / 60000) + " minutes");

                for (Map.Entry<IAiContextEngine.ContextType, Object> entry :
                     prediction.predictedValues.entrySet()) {
                    addResult("    • " + entry.getKey() + ": " + entry.getValue());
                }
            }
        }

        // Show anomaly detection results
        if (anomalyDetector != null) {
            AnomalyDetector.AnomalyStatistics anomalyStats = anomalyDetector.getAnomalyStatistics();
            addResult("  🚨 Anomaly Detection:");
            addResult("    • Total Anomalies: " + anomalyStats.totalAnomalies);
            addResult("    • Anomaly Rate: " + String.format("%.3f%%", anomalyStats.anomalyRate * 100));

            if (anomalyStats.anomaliesByType != null) {
                for (Map.Entry<IAiContextEngine.ContextType, Integer> entry :
                     anomalyStats.anomaliesByType.entrySet()) {
                    addResult("    • " + entry.getKey() + ": " + entry.getValue());
                }
            }
        }

        // Show pattern matching statistics
        if (patternMatcher != null) {
            PatternMatcher.PatternStatistics patternStats = patternMatcher.getPatternStatistics();
            addResult("  🎯 Pattern Matching:");
            addResult("    • Total Patterns: " + patternStats.totalLearnedPatterns);
            addResult("    • High Confidence: " + patternStats.highConfidencePatterns);
            addResult("    • Recent Matches: " + patternStats.recentMatches);
        }
    }
    
    /**
     * Demo execution callback
     */
    private class DemoExecutionCallback implements IAiPlanningOrchestration.IExecutionCallback {
        @Override
        public void onPlanStarted(String planId) {
            runOnUiThread(() -> addResult("▶️ Plan execution started: " + planId));
        }
        
        @Override
        public void onActionStarted(String planId, IAiPlanningOrchestration.PlanAction action) {
            runOnUiThread(() -> addResult("🔄 Executing: " + action.description));
        }
        
        @Override
        public void onActionCompleted(String planId, IAiPlanningOrchestration.PlanAction action, boolean success) {
            runOnUiThread(() -> {
                String status = success ? "✅" : "❌";
                addResult(status + " " + action.description + " - " + 
                    (success ? "completed" : "failed"));
            });
        }
        
        @Override
        public void onPlanCompleted(String planId, IAiPlanningOrchestration.ExecutionResult result) {
            runOnUiThread(() -> {
                String status = result.status == IAiPlanningOrchestration.ExecutionStatus.COMPLETED ? "✅" : "❌";
                addResult(status + " Plan completed: " + result.message);
                showProgress(false);
            });
        }
        
        @Override
        public void onUserConfirmationRequired(String planId, IAiPlanningOrchestration.PlanAction action) {
            runOnUiThread(() -> {
                addResult("⚠️ User confirmation required for: " + action.description);
                // In a real app, would show confirmation dialog
                addResult("✅ Auto-confirmed for demo");
            });
        }
    }
    
    /**
     * Update status text
     */
    private void updateStatus(String status) {
        runOnUiThread(() -> {
            statusText.setText(status);
            Log.d(TAG, "Status: " + status);
        });
    }
    
    /**
     * Enable/disable UI
     */
    private void enableUI(boolean enabled) {
        executeButton.setEnabled(enabled);
        contextButton.setEnabled(enabled);
        learningButton.setEnabled(enabled);
        automationButton.setEnabled(enabled);
        mlInsightsButton.setEnabled(enabled);
        queryInput.setEnabled(enabled);
    }
    
    /**
     * Show/hide progress
     */
    private void showProgress(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
    }
    
    /**
     * Add result to list
     */
    private void addResult(String result) {
        runOnUiThread(() -> {
            demoResults.add(result);
            resultsAdapter.notifyDataSetChanged();
            resultsListView.smoothScrollToPosition(demoResults.size() - 1);
        });
    }
    
    /**
     * Get current time of day
     */
    private String getCurrentTimeOfDay() {
        Calendar cal = Calendar.getInstance();
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        
        if (hour < 6) return "night";
        else if (hour < 12) return "morning";
        else if (hour < 18) return "afternoon";
        else return "evening";
    }
    
    /**
     * Cleanup resources
     */
    private void cleanup() {
        if (automationEngine != null) {
            automationEngine.stop();
        }

        if (aiServiceManager != null) {
            aiServiceManager.stopAiServices();
        }

        Log.d(TAG, "Demo cleanup completed");
    }
}
