package com.jarvis.core.ai.ml;

import android.util.Log;
import com.jarvis.core.ai.interfaces.IAiContextEngine;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Anomaly Detector for context data
 * Detects unusual patterns and outliers in context information
 */
public class AnomalyDetector {
    
    private static final String TAG = "AnomalyDetector";
    private static final int MIN_SAMPLES_FOR_DETECTION = 10;
    private static final float ANOMALY_THRESHOLD = 2.0f; // Standard deviations
    private static final long LEARNING_WINDOW = 86400000; // 24 hours
    
    // Statistical models for each context type
    private final Map<IAiContextEngine.ContextType, StatisticalModel> models = new ConcurrentHashMap<>();
    
    // Historical data for learning
    private final Map<IAiContextEngine.ContextType, List<ContextSample>> historicalData = new ConcurrentHashMap<>();
    
    // Anomaly patterns
    private final List<AnomalyPattern> detectedAnomalies = new ArrayList<>();
    
    public AnomalyDetector() {
        initializeModels();
        Log.d(TAG, "Anomaly Detector initialized");
    }
    
    /**
     * Detect if context data is anomalous
     */
    public boolean detectAnomaly(IAiContextEngine.ContextData contextData) {
        try {
            // Add to historical data
            addToHistoricalData(contextData);
            
            // Get statistical model for this context type
            StatisticalModel model = models.get(contextData.type);
            if (model == null) {
                return false; // No model available
            }
            
            // Update model with new data
            model.updateModel(contextData);
            
            // Check if we have enough samples for detection
            if (model.getSampleCount() < MIN_SAMPLES_FOR_DETECTION) {
                return false; // Not enough data for reliable detection
            }
            
            // Perform anomaly detection
            boolean isAnomaly = performAnomalyDetection(contextData, model);
            
            if (isAnomaly) {
                recordAnomaly(contextData, model);
                Log.w(TAG, "Anomaly detected in " + contextData.type + ": " + 
                    getAnomalyDescription(contextData));
            }
            
            return isAnomaly;
            
        } catch (Exception e) {
            Log.e(TAG, "Error in anomaly detection", e);
            return false;
        }
    }
    
    /**
     * Get anomaly statistics
     */
    public AnomalyStatistics getAnomalyStatistics() {
        AnomalyStatistics stats = new AnomalyStatistics();
        
        stats.totalAnomalies = detectedAnomalies.size();
        stats.anomaliesByType = new HashMap<>();
        
        for (AnomalyPattern anomaly : detectedAnomalies) {
            stats.anomaliesByType.merge(anomaly.contextType, 1, Integer::sum);
        }
        
        // Calculate anomaly rate
        long totalSamples = historicalData.values().stream()
            .mapToLong(List::size)
            .sum();
        
        stats.anomalyRate = totalSamples > 0 ? 
            (float) stats.totalAnomalies / totalSamples : 0.0f;
        
        return stats;
    }
    
    /**
     * Clear old anomaly data
     */
    public void clearOldAnomalies(long olderThan) {
        detectedAnomalies.removeIf(anomaly -> anomaly.timestamp < olderThan);
        
        for (List<ContextSample> samples : historicalData.values()) {
            samples.removeIf(sample -> sample.timestamp < olderThan);
        }
        
        // Rebuild models
        rebuildModels();
    }
    
    /**
     * Initialize statistical models
     */
    private void initializeModels() {
        for (IAiContextEngine.ContextType type : IAiContextEngine.ContextType.values()) {
            models.put(type, new StatisticalModel(type));
        }
    }
    
    /**
     * Add context data to historical data
     */
    private void addToHistoricalData(IAiContextEngine.ContextData contextData) {
        List<ContextSample> samples = historicalData.computeIfAbsent(
            contextData.type, k -> new ArrayList<>());
        
        samples.add(new ContextSample(contextData));
        
        // Limit historical data size
        if (samples.size() > 1000) {
            samples.remove(0);
        }
    }
    
    /**
     * Perform anomaly detection using statistical methods
     */
    private boolean performAnomalyDetection(IAiContextEngine.ContextData contextData, 
                                          StatisticalModel model) {
        
        // Extract numerical features from context data
        List<Float> features = extractNumericalFeatures(contextData);
        
        if (features.isEmpty()) {
            return false; // No numerical features to analyze
        }
        
        // Check each feature for anomalies
        for (int i = 0; i < features.size(); i++) {
            float value = features.get(i);
            
            if (model.isOutlier(i, value)) {
                return true; // Found an outlier
            }
        }
        
        // Check temporal anomalies
        if (isTemporalAnomaly(contextData)) {
            return true;
        }
        
        // Check frequency anomalies
        if (isFrequencyAnomaly(contextData)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Extract numerical features from context data
     */
    private List<Float> extractNumericalFeatures(IAiContextEngine.ContextData contextData) {
        List<Float> features = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : contextData.data.entrySet()) {
            Object value = entry.getValue();
            
            if (value instanceof Number) {
                features.add(((Number) value).floatValue());
            } else if (value instanceof String) {
                // Convert string to numerical feature (hash code normalized)
                features.add((float) Math.abs(value.hashCode()) / Integer.MAX_VALUE);
            } else if (value instanceof Boolean) {
                features.add(((Boolean) value) ? 1.0f : 0.0f);
            }
        }
        
        // Add temporal features
        features.add((float) (contextData.timestamp % 86400000) / 86400000); // Time of day
        features.add(contextData.confidence);
        
        return features;
    }
    
    /**
     * Check for temporal anomalies
     */
    private boolean isTemporalAnomaly(IAiContextEngine.ContextData contextData) {
        List<ContextSample> samples = historicalData.get(contextData.type);
        if (samples == null || samples.size() < 5) {
            return false;
        }
        
        // Check if this context type typically occurs at this time
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(contextData.timestamp);
        int currentHour = cal.get(Calendar.HOUR_OF_DAY);
        
        // Count occurrences in this hour from historical data
        int hourOccurrences = 0;
        int totalOccurrences = 0;
        
        for (ContextSample sample : samples) {
            cal.setTimeInMillis(sample.timestamp);
            int sampleHour = cal.get(Calendar.HOUR_OF_DAY);
            
            if (sampleHour == currentHour) {
                hourOccurrences++;
            }
            totalOccurrences++;
        }
        
        // Calculate expected frequency
        float expectedFrequency = (float) hourOccurrences / totalOccurrences;
        
        // Consider it anomalous if this hour has very low historical frequency
        return expectedFrequency < 0.05f && totalOccurrences > 20;
    }
    
    /**
     * Check for frequency anomalies
     */
    private boolean isFrequencyAnomaly(IAiContextEngine.ContextData contextData) {
        List<ContextSample> samples = historicalData.get(contextData.type);
        if (samples == null || samples.size() < 10) {
            return false;
        }
        
        // Calculate recent frequency
        long recentWindow = 3600000; // 1 hour
        long currentTime = System.currentTimeMillis();
        
        int recentCount = 0;
        for (ContextSample sample : samples) {
            if (currentTime - sample.timestamp <= recentWindow) {
                recentCount++;
            }
        }
        
        // Calculate historical average frequency
        long totalTimeSpan = currentTime - samples.get(0).timestamp;
        float avgFrequency = (float) samples.size() / (totalTimeSpan / recentWindow);
        
        // Check if recent frequency is significantly higher than average
        float recentFrequency = (float) recentCount;
        
        return recentFrequency > avgFrequency * 3.0f; // 3x higher than average
    }
    
    /**
     * Record detected anomaly
     */
    private void recordAnomaly(IAiContextEngine.ContextData contextData, StatisticalModel model) {
        AnomalyPattern anomaly = new AnomalyPattern();
        anomaly.contextType = contextData.type;
        anomaly.timestamp = contextData.timestamp;
        anomaly.anomalyScore = calculateAnomalyScore(contextData, model);
        anomaly.description = getAnomalyDescription(contextData);
        anomaly.contextData = new HashMap<>(contextData.data);
        
        detectedAnomalies.add(anomaly);
        
        // Limit anomaly history
        if (detectedAnomalies.size() > 500) {
            detectedAnomalies.remove(0);
        }
    }
    
    /**
     * Calculate anomaly score
     */
    private float calculateAnomalyScore(IAiContextEngine.ContextData contextData, 
                                      StatisticalModel model) {
        List<Float> features = extractNumericalFeatures(contextData);
        float maxScore = 0.0f;
        
        for (int i = 0; i < features.size(); i++) {
            float score = model.getAnomalyScore(i, features.get(i));
            maxScore = Math.max(maxScore, score);
        }
        
        return maxScore;
    }
    
    /**
     * Get anomaly description
     */
    private String getAnomalyDescription(IAiContextEngine.ContextData contextData) {
        StringBuilder description = new StringBuilder();
        description.append("Unusual ").append(contextData.type.name().toLowerCase());
        
        // Add specific details based on context type
        switch (contextData.type) {
            case USER_ACTIVITY:
                if (contextData.data.containsKey("currentApp")) {
                    description.append(" - app: ").append(contextData.data.get("currentApp"));
                }
                break;
            case DEVICE_SENSORS:
                if (contextData.data.containsKey("batteryLevel")) {
                    description.append(" - battery: ").append(contextData.data.get("batteryLevel"));
                }
                break;
            case NOTIFICATIONS:
                if (contextData.data.containsKey("urgentCount")) {
                    description.append(" - urgent: ").append(contextData.data.get("urgentCount"));
                }
                break;
        }
        
        return description.toString();
    }
    
    /**
     * Rebuild statistical models
     */
    private void rebuildModels() {
        for (Map.Entry<IAiContextEngine.ContextType, StatisticalModel> entry : models.entrySet()) {
            IAiContextEngine.ContextType type = entry.getKey();
            StatisticalModel model = entry.getValue();
            
            model.reset();
            
            List<ContextSample> samples = historicalData.get(type);
            if (samples != null) {
                for (ContextSample sample : samples) {
                    model.updateModel(sample.contextData);
                }
            }
        }
    }
    
    /**
     * Statistical model for anomaly detection
     */
    private static class StatisticalModel {
        private final IAiContextEngine.ContextType contextType;
        private final List<List<Float>> featureHistory;
        private final List<Float> featureMeans;
        private final List<Float> featureStdDevs;
        private int sampleCount;
        
        public StatisticalModel(IAiContextEngine.ContextType contextType) {
            this.contextType = contextType;
            this.featureHistory = new ArrayList<>();
            this.featureMeans = new ArrayList<>();
            this.featureStdDevs = new ArrayList<>();
            this.sampleCount = 0;
        }
        
        public void updateModel(IAiContextEngine.ContextData contextData) {
            List<Float> features = extractNumericalFeatures(contextData);
            
            // Ensure feature vectors have consistent size
            while (featureHistory.size() < features.size()) {
                featureHistory.add(new ArrayList<>());
                featureMeans.add(0.0f);
                featureStdDevs.add(0.0f);
            }
            
            // Add features to history
            for (int i = 0; i < features.size(); i++) {
                featureHistory.get(i).add(features.get(i));
            }
            
            sampleCount++;
            
            // Update statistics
            updateStatistics();
        }
        
        public boolean isOutlier(int featureIndex, float value) {
            if (featureIndex >= featureMeans.size() || sampleCount < MIN_SAMPLES_FOR_DETECTION) {
                return false;
            }
            
            float mean = featureMeans.get(featureIndex);
            float stdDev = featureStdDevs.get(featureIndex);
            
            if (stdDev == 0) return false; // No variation
            
            float zScore = Math.abs(value - mean) / stdDev;
            return zScore > ANOMALY_THRESHOLD;
        }
        
        public float getAnomalyScore(int featureIndex, float value) {
            if (featureIndex >= featureMeans.size() || sampleCount < MIN_SAMPLES_FOR_DETECTION) {
                return 0.0f;
            }
            
            float mean = featureMeans.get(featureIndex);
            float stdDev = featureStdDevs.get(featureIndex);
            
            if (stdDev == 0) return 0.0f;
            
            return Math.abs(value - mean) / stdDev;
        }
        
        public int getSampleCount() {
            return sampleCount;
        }
        
        public void reset() {
            featureHistory.clear();
            featureMeans.clear();
            featureStdDevs.clear();
            sampleCount = 0;
        }
        
        private void updateStatistics() {
            for (int i = 0; i < featureHistory.size(); i++) {
                List<Float> values = featureHistory.get(i);
                
                if (values.isEmpty()) continue;
                
                // Calculate mean
                float sum = values.stream().reduce(0.0f, Float::sum);
                float mean = sum / values.size();
                featureMeans.set(i, mean);
                
                // Calculate standard deviation
                float variance = values.stream()
                    .map(v -> (v - mean) * (v - mean))
                    .reduce(0.0f, Float::sum) / values.size();
                
                featureStdDevs.set(i, (float) Math.sqrt(variance));
            }
        }
        
        private List<Float> extractNumericalFeatures(IAiContextEngine.ContextData contextData) {
            // Same implementation as in AnomalyDetector
            List<Float> features = new ArrayList<>();
            
            for (Map.Entry<String, Object> entry : contextData.data.entrySet()) {
                Object value = entry.getValue();
                
                if (value instanceof Number) {
                    features.add(((Number) value).floatValue());
                } else if (value instanceof String) {
                    features.add((float) Math.abs(value.hashCode()) / Integer.MAX_VALUE);
                } else if (value instanceof Boolean) {
                    features.add(((Boolean) value) ? 1.0f : 0.0f);
                }
            }
            
            features.add((float) (contextData.timestamp % 86400000) / 86400000);
            features.add(contextData.confidence);
            
            return features;
        }
    }
    
    /**
     * Context sample for historical data
     */
    private static class ContextSample {
        public final IAiContextEngine.ContextData contextData;
        public final long timestamp;
        
        public ContextSample(IAiContextEngine.ContextData contextData) {
            this.contextData = contextData;
            this.timestamp = contextData.timestamp;
        }
    }
    
    /**
     * Anomaly pattern representation
     */
    public static class AnomalyPattern {
        public IAiContextEngine.ContextType contextType;
        public long timestamp;
        public float anomalyScore;
        public String description;
        public Map<String, Object> contextData;
    }
    
    /**
     * Anomaly statistics
     */
    public static class AnomalyStatistics {
        public int totalAnomalies;
        public Map<IAiContextEngine.ContextType, Integer> anomaliesByType;
        public float anomalyRate;
    }
}
