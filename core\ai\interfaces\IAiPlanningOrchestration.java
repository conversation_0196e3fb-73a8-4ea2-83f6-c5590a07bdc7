package com.jarvis.core.ai.interfaces;

import android.content.Intent;
import java.util.*;

/**
 * Interface for AI Planning and Orchestration Service
 * Handles complex task planning and execution coordination
 */
public interface IAiPlanningOrchestration {
    
    /**
     * Action types that can be executed by the OS
     */
    enum ActionType {
        OPEN_APP,           // Open application
        SEND_INTENT,        // Send system intent
        SET_SETTING,        // Modify system setting
        COMPOSE_MESSAGE,    // Create message/email
        SCHEDULE_EVENT,     // Add calendar event
        MAKE_CALL,          // Initiate phone call
        NAVIGATE,           // Start navigation
        CONTROL_DEVICE,     // Control smart home devices
        SEARCH_WEB,         // Perform web search
        CUSTOM_ACTION       // Custom defined action
    }
    
    /**
     * Execution status of actions
     */
    enum ExecutionStatus {
        PENDING,            // Waiting to execute
        IN_PROGRESS,        // Currently executing
        COMPLETED,          // Successfully completed
        FAILED,             // Failed to execute
        CANCELLED,          // Cancelled by user
        REQUIRES_PERMISSION // Needs user permission
    }
    
    /**
     * Individual action in a plan
     */
    class PlanAction {
        public String id;
        public ActionType type;
        public Map<String, Object> parameters;
        public List<String> dependencies;
        public ExecutionStatus status;
        public String description;
        public boolean requiresUserConfirmation;
        
        public PlanAction(ActionType type, Map<String, Object> parameters) {
            this.id = generateActionId();
            this.type = type;
            this.parameters = parameters;
            this.status = ExecutionStatus.PENDING;
            this.requiresUserConfirmation = false;
        }
        
        private String generateActionId() {
            return "action_" + System.currentTimeMillis() + "_" + hashCode();
        }
    }
    
    /**
     * Execution plan containing multiple actions
     */
    class ExecutionPlan {
        public String planId;
        public String userQuery;
        public List<PlanAction> actions;
        public ExecutionStatus overallStatus;
        public long createdTimestamp;
        public long estimatedDuration;
        public String description;
        
        public ExecutionPlan(String userQuery) {
            this.planId = generatePlanId();
            this.userQuery = userQuery;
            this.actions = new ArrayList<>();
            this.overallStatus = ExecutionStatus.PENDING;
            this.createdTimestamp = System.currentTimeMillis();
        }
        
        private String generatePlanId() {
            return "plan_" + System.currentTimeMillis();
        }
    }
    
    /**
     * Result of plan execution
     */
    class ExecutionResult {
        public String planId;
        public ExecutionStatus status;
        public String message;
        public Map<String, Object> resultData;
        public List<String> failedActions;
        
        public ExecutionResult(String planId, ExecutionStatus status) {
            this.planId = planId;
            this.status = status;
            this.resultData = new HashMap<>();
            this.failedActions = new ArrayList<>();
        }
    }
    
    /**
     * Create execution plan from natural language query
     * @param userQuery Natural language description of desired task
     * @param context Current context from AiContextEngine
     * @return Generated execution plan
     */
    ExecutionPlan createPlan(String userQuery, IAiContextEngine.FusedContext context);
    
    /**
     * Execute a plan
     * @param plan Plan to execute
     * @param callback Callback for execution updates
     * @return Execution result
     */
    ExecutionResult executePlan(ExecutionPlan plan, IExecutionCallback callback);
    
    /**
     * Cancel plan execution
     * @param planId ID of plan to cancel
     * @return true if successfully cancelled
     */
    boolean cancelPlan(String planId);
    
    /**
     * Get plan execution status
     * @param planId ID of plan to check
     * @return Current execution status
     */
    ExecutionStatus getPlanStatus(String planId);
    
    /**
     * Get active plans
     * @return List of currently active plans
     */
    List<ExecutionPlan> getActivePlans();
    
    /**
     * Get plan history
     * @param limit Maximum number of plans to return
     * @return List of historical plans
     */
    List<ExecutionPlan> getPlanHistory(int limit);
    
    /**
     * Validate if action can be executed
     * @param action Action to validate
     * @return true if action can be executed
     */
    boolean validateAction(PlanAction action);
    
    /**
     * Get available actions for current context
     * @param context Current context
     * @return List of available action types
     */
    List<ActionType> getAvailableActions(IAiContextEngine.FusedContext context);
    
    /**
     * Interface for execution callbacks
     */
    interface IExecutionCallback {
        void onPlanStarted(String planId);
        void onActionStarted(String planId, PlanAction action);
        void onActionCompleted(String planId, PlanAction action, boolean success);
        void onPlanCompleted(String planId, ExecutionResult result);
        void onUserConfirmationRequired(String planId, PlanAction action);
    }
}
