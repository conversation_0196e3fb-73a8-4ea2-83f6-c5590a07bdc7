package com.jarvis.systemui.jarvis;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import com.jarvis.framework.services.AiSystemServiceManager;
import com.jarvis.core.ai.interfaces.IAiContextEngine;
import com.jarvis.core.ai.interfaces.IAiPlanningOrchestration;

/**
 * Jarvis Assistant Overlay
 * System-wide conversational interface that can be accessed from any screen
 */
public class JarvisAssistantOverlay {
    
    private static final String TAG = "JarvisAssistantOverlay";
    private static final int ANIMATION_DURATION = 300;
    private static final float OVERLAY_ALPHA = 0.95f;
    
    private Context context;
    private WindowManager windowManager;
    private AiSystemServiceManager aiServiceManager;
    
    // UI Components
    private View overlayView;
    private JarvisConversationView conversationView;
    private JarvisFloatingButton floatingButton;
    private JarvisProactivePanel proactivePanel;
    
    // State
    private boolean isOverlayVisible = false;
    private boolean isFloatingButtonVisible = true;
    private boolean isProactivePanelVisible = false;
    
    // Handlers
    private Handler mainHandler;
    private ConversationHandler conversationHandler;
    
    /**
     * Initialize Jarvis Assistant Overlay
     */
    public JarvisAssistantOverlay(Context context) {
        this.context = context;
        this.windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        this.aiServiceManager = AiSystemServiceManager.getInstance(context);
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        initializeComponents();
        setupConversationHandler();
    }
    
    /**
     * Initialize UI components
     */
    private void initializeComponents() {
        // Create floating button
        floatingButton = new JarvisFloatingButton(context);
        floatingButton.setOnClickListener(v -> showOverlay());
        floatingButton.setOnLongClickListener(v -> {
            startVoiceInput();
            return true;
        });
        
        // Create conversation view
        conversationView = new JarvisConversationView(context);
        conversationView.setConversationListener(new ConversationListener());
        
        // Create proactive panel
        proactivePanel = new JarvisProactivePanel(context);
        proactivePanel.setProactiveActionListener(new ProactiveActionListener());
        
        Log.d(TAG, "UI components initialized");
    }
    
    /**
     * Setup conversation handler
     */
    private void setupConversationHandler() {
        conversationHandler = new ConversationHandler(context, aiServiceManager);
        conversationHandler.setResponseListener(new ConversationResponseListener());
    }
    
    /**
     * Show the Jarvis overlay
     */
    public void showOverlay() {
        if (isOverlayVisible) return;
        
        try {
            createOverlayView();
            addOverlayToWindow();
            animateOverlayIn();
            
            isOverlayVisible = true;
            hideFloatingButton();
            
            Log.d(TAG, "Overlay shown");
        } catch (Exception e) {
            Log.e(TAG, "Error showing overlay", e);
        }
    }
    
    /**
     * Hide the Jarvis overlay
     */
    public void hideOverlay() {
        if (!isOverlayVisible) return;
        
        animateOverlayOut(() -> {
            removeOverlayFromWindow();
            isOverlayVisible = false;
            showFloatingButton();
            
            Log.d(TAG, "Overlay hidden");
        });
    }
    
    /**
     * Show proactive suggestions
     */
    public void showProactiveSuggestions(List<String> suggestions) {
        if (isProactivePanelVisible) return;
        
        try {
            proactivePanel.setSuggestions(suggestions);
            addProactivePanelToWindow();
            animateProactivePanelIn();
            
            isProactivePanelVisible = true;
            
            // Auto-hide after 10 seconds
            mainHandler.postDelayed(this::hideProactiveSuggestions, 10000);
            
            Log.d(TAG, "Proactive suggestions shown");
        } catch (Exception e) {
            Log.e(TAG, "Error showing proactive suggestions", e);
        }
    }
    
    /**
     * Hide proactive suggestions
     */
    public void hideProactiveSuggestions() {
        if (!isProactivePanelVisible) return;
        
        animateProactivePanelOut(() -> {
            removeProactivePanelFromWindow();
            isProactivePanelVisible = false;
            
            Log.d(TAG, "Proactive suggestions hidden");
        });
    }
    
    /**
     * Show floating button
     */
    public void showFloatingButton() {
        if (isFloatingButtonVisible) return;
        
        try {
            addFloatingButtonToWindow();
            animateFloatingButtonIn();
            isFloatingButtonVisible = true;
            
            Log.d(TAG, "Floating button shown");
        } catch (Exception e) {
            Log.e(TAG, "Error showing floating button", e);
        }
    }
    
    /**
     * Hide floating button
     */
    public void hideFloatingButton() {
        if (!isFloatingButtonVisible) return;
        
        animateFloatingButtonOut(() -> {
            removeFloatingButtonFromWindow();
            isFloatingButtonVisible = false;
            
            Log.d(TAG, "Floating button hidden");
        });
    }
    
    /**
     * Start voice input
     */
    private void startVoiceInput() {
        if (!isOverlayVisible) {
            showOverlay();
        }
        
        conversationView.startVoiceInput();
        Log.d(TAG, "Voice input started");
    }
    
    /**
     * Create overlay view
     */
    private void createOverlayView() {
        overlayView = LayoutInflater.from(context).inflate(R.layout.jarvis_overlay, null);
        
        // Add conversation view to overlay
        FrameLayout conversationContainer = overlayView.findViewById(R.id.conversation_container);
        conversationContainer.addView(conversationView);
        
        // Setup close button
        View closeButton = overlayView.findViewById(R.id.close_button);
        closeButton.setOnClickListener(v -> hideOverlay());
        
        // Setup background touch to close
        View background = overlayView.findViewById(R.id.overlay_background);
        background.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                hideOverlay();
                return true;
            }
            return false;
        });
    }
    
    /**
     * Add overlay to window
     */
    private void addOverlayToWindow() {
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
            PixelFormat.TRANSLUCENT
        );
        
        params.gravity = Gravity.CENTER;
        windowManager.addView(overlayView, params);
    }
    
    /**
     * Remove overlay from window
     */
    private void removeOverlayFromWindow() {
        if (overlayView != null && overlayView.getParent() != null) {
            windowManager.removeView(overlayView);
            overlayView = null;
        }
    }
    
    /**
     * Add floating button to window
     */
    private void addFloatingButtonToWindow() {
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        );
        
        params.gravity = Gravity.BOTTOM | Gravity.END;
        params.x = 50;
        params.y = 200;
        
        windowManager.addView(floatingButton, params);
    }
    
    /**
     * Remove floating button from window
     */
    private void removeFloatingButtonFromWindow() {
        if (floatingButton != null && floatingButton.getParent() != null) {
            windowManager.removeView(floatingButton);
        }
    }
    
    /**
     * Add proactive panel to window
     */
    private void addProactivePanelToWindow() {
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        );
        
        params.gravity = Gravity.TOP;
        windowManager.addView(proactivePanel, params);
    }
    
    /**
     * Remove proactive panel from window
     */
    private void removeProactivePanelFromWindow() {
        if (proactivePanel != null && proactivePanel.getParent() != null) {
            windowManager.removeView(proactivePanel);
        }
    }
    
    /**
     * Animate overlay in
     */
    private void animateOverlayIn() {
        overlayView.setAlpha(0f);
        overlayView.setScaleX(0.8f);
        overlayView.setScaleY(0.8f);
        
        overlayView.animate()
            .alpha(OVERLAY_ALPHA)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(ANIMATION_DURATION)
            .start();
    }
    
    /**
     * Animate overlay out
     */
    private void animateOverlayOut(Runnable onComplete) {
        overlayView.animate()
            .alpha(0f)
            .scaleX(0.8f)
            .scaleY(0.8f)
            .setDuration(ANIMATION_DURATION)
            .setListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    if (onComplete != null) {
                        onComplete.run();
                    }
                }
            })
            .start();
    }
    
    /**
     * Animate floating button in
     */
    private void animateFloatingButtonIn() {
        floatingButton.setAlpha(0f);
        floatingButton.setScaleX(0f);
        floatingButton.setScaleY(0f);
        
        floatingButton.animate()
            .alpha(1f)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(ANIMATION_DURATION)
            .start();
    }
    
    /**
     * Animate floating button out
     */
    private void animateFloatingButtonOut(Runnable onComplete) {
        floatingButton.animate()
            .alpha(0f)
            .scaleX(0f)
            .scaleY(0f)
            .setDuration(ANIMATION_DURATION)
            .setListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    if (onComplete != null) {
                        onComplete.run();
                    }
                }
            })
            .start();
    }
    
    /**
     * Animate proactive panel in
     */
    private void animateProactivePanelIn() {
        proactivePanel.setTranslationY(-proactivePanel.getHeight());
        proactivePanel.animate()
            .translationY(0f)
            .setDuration(ANIMATION_DURATION)
            .start();
    }
    
    /**
     * Animate proactive panel out
     */
    private void animateProactivePanelOut(Runnable onComplete) {
        proactivePanel.animate()
            .translationY(-proactivePanel.getHeight())
            .setDuration(ANIMATION_DURATION)
            .setListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    if (onComplete != null) {
                        onComplete.run();
                    }
                }
            })
            .start();
    }
    
    /**
     * Conversation listener implementation
     */
    private class ConversationListener implements JarvisConversationView.ConversationListener {
        @Override
        public void onUserInput(String input) {
            conversationHandler.processUserInput(input);
        }
        
        @Override
        public void onVoiceInputStarted() {
            Log.d(TAG, "Voice input started");
        }
        
        @Override
        public void onVoiceInputCompleted(String transcript) {
            conversationHandler.processUserInput(transcript);
        }
    }
    
    /**
     * Conversation response listener implementation
     */
    private class ConversationResponseListener implements ConversationHandler.ResponseListener {
        @Override
        public void onResponse(String response) {
            mainHandler.post(() -> conversationView.addAssistantMessage(response));
        }
        
        @Override
        public void onActionPlan(IAiPlanningOrchestration.ExecutionPlan plan) {
            mainHandler.post(() -> conversationView.showActionPlan(plan));
        }
        
        @Override
        public void onError(String error) {
            mainHandler.post(() -> conversationView.showError(error));
        }
    }
    
    /**
     * Proactive action listener implementation
     */
    private class ProactiveActionListener implements JarvisProactivePanel.ProactiveActionListener {
        @Override
        public void onSuggestionSelected(String suggestion) {
            hideProactiveSuggestions();
            showOverlay();
            conversationView.addUserMessage(suggestion);
            conversationHandler.processUserInput(suggestion);
        }
        
        @Override
        public void onSuggestionDismissed() {
            hideProactiveSuggestions();
        }
    }
}
