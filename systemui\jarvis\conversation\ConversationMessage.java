package com.jarvis.systemui.jarvis.conversation;

import com.jarvis.core.ai.interfaces.IAiPlanningOrchestration;
import java.util.HashMap;
import java.util.Map;

/**
 * Conversation Message Model
 * Represents different types of messages in the Jarvis conversation interface
 */
public class ConversationMessage {
    
    public enum Type {
        USER,           // User input message
        ASSISTANT,      // Assistant response
        ACTION_PLAN,    // Execution plan display
        SYSTEM,         // System notifications
        ERROR,          // Error messages
        SUGGESTION,     // Proactive suggestions
        CONTEXT_UPDATE, // Context change notifications
        MEDIA,          // Media content (images, audio, etc.)
        QUICK_REPLY     // Quick reply options
    }
    
    public enum Status {
        SENT,           // Message sent
        DELIVERED,      // Message delivered
        PROCESSING,     // Being processed
        COMPLETED,      // Processing completed
        FAILED          // Processing failed
    }
    
    // Core message properties
    public String content;
    public Type type;
    public long timestamp;
    public Status status;
    public String messageId;
    
    // Extended properties
    public Map<String, Object> metadata;
    public IAiPlanningOrchestration.ExecutionPlan executionPlan;
    public String contextInfo;
    public float confidence;
    public boolean isProactive;
    
    // Media properties
    public String mediaUrl;
    public String mediaType;
    public String thumbnailUrl;
    
    // Interaction properties
    public boolean isInteractive;
    public String[] quickReplies;
    public String actionId;
    
    /**
     * Constructor for basic message
     */
    public ConversationMessage(String content, Type type, long timestamp) {
        this.content = content;
        this.type = type;
        this.timestamp = timestamp;
        this.status = Status.SENT;
        this.messageId = generateMessageId();
        this.metadata = new HashMap<>();
        this.confidence = 1.0f;
        this.isProactive = false;
        this.isInteractive = false;
    }
    
    /**
     * Constructor for assistant message with confidence
     */
    public ConversationMessage(String content, Type type, long timestamp, float confidence) {
        this(content, type, timestamp);
        this.confidence = confidence;
    }
    
    /**
     * Constructor for proactive message
     */
    public ConversationMessage(String content, Type type, long timestamp, boolean isProactive) {
        this(content, type, timestamp);
        this.isProactive = isProactive;
    }
    
    /**
     * Create user message
     */
    public static ConversationMessage createUserMessage(String content) {
        return new ConversationMessage(content, Type.USER, System.currentTimeMillis());
    }
    
    /**
     * Create assistant message
     */
    public static ConversationMessage createAssistantMessage(String content) {
        return new ConversationMessage(content, Type.ASSISTANT, System.currentTimeMillis());
    }
    
    /**
     * Create assistant message with confidence
     */
    public static ConversationMessage createAssistantMessage(String content, float confidence) {
        return new ConversationMessage(content, Type.ASSISTANT, System.currentTimeMillis(), confidence);
    }
    
    /**
     * Create system message
     */
    public static ConversationMessage createSystemMessage(String content) {
        return new ConversationMessage(content, Type.SYSTEM, System.currentTimeMillis());
    }
    
    /**
     * Create error message
     */
    public static ConversationMessage createErrorMessage(String content) {
        return new ConversationMessage(content, Type.ERROR, System.currentTimeMillis());
    }
    
    /**
     * Create proactive suggestion
     */
    public static ConversationMessage createProactiveSuggestion(String content) {
        return new ConversationMessage(content, Type.SUGGESTION, System.currentTimeMillis(), true);
    }
    
    /**
     * Create action plan message
     */
    public static ConversationMessage createActionPlanMessage(IAiPlanningOrchestration.ExecutionPlan plan) {
        StringBuilder planText = new StringBuilder();
        planText.append("📋 Here's what I'll do:\n\n");
        
        for (int i = 0; i < plan.actions.size(); i++) {
            IAiPlanningOrchestration.PlanAction action = plan.actions.get(i);
            planText.append(String.format("%d. %s\n", i + 1, action.description));
        }
        
        ConversationMessage message = new ConversationMessage(
            planText.toString(), Type.ACTION_PLAN, System.currentTimeMillis());
        message.executionPlan = plan;
        message.isInteractive = true;
        
        return message;
    }
    
    /**
     * Create context update message
     */
    public static ConversationMessage createContextUpdate(String contextInfo) {
        ConversationMessage message = new ConversationMessage(
            "Context updated: " + contextInfo, Type.CONTEXT_UPDATE, System.currentTimeMillis());
        message.contextInfo = contextInfo;
        return message;
    }
    
    /**
     * Create media message
     */
    public static ConversationMessage createMediaMessage(String content, String mediaUrl, String mediaType) {
        ConversationMessage message = new ConversationMessage(content, Type.MEDIA, System.currentTimeMillis());
        message.mediaUrl = mediaUrl;
        message.mediaType = mediaType;
        return message;
    }
    
    /**
     * Create quick reply message
     */
    public static ConversationMessage createQuickReplyMessage(String content, String[] quickReplies) {
        ConversationMessage message = new ConversationMessage(content, Type.QUICK_REPLY, System.currentTimeMillis());
        message.quickReplies = quickReplies;
        message.isInteractive = true;
        return message;
    }
    
    /**
     * Add metadata
     */
    public ConversationMessage addMetadata(String key, Object value) {
        metadata.put(key, value);
        return this;
    }
    
    /**
     * Get metadata
     */
    public Object getMetadata(String key) {
        return metadata.get(key);
    }
    
    /**
     * Set status
     */
    public ConversationMessage setStatus(Status status) {
        this.status = status;
        return this;
    }
    
    /**
     * Set context info
     */
    public ConversationMessage setContextInfo(String contextInfo) {
        this.contextInfo = contextInfo;
        return this;
    }
    
    /**
     * Set action ID for interactive messages
     */
    public ConversationMessage setActionId(String actionId) {
        this.actionId = actionId;
        this.isInteractive = true;
        return this;
    }
    
    /**
     * Check if message is from user
     */
    public boolean isFromUser() {
        return type == Type.USER;
    }
    
    /**
     * Check if message is from assistant
     */
    public boolean isFromAssistant() {
        return type == Type.ASSISTANT || type == Type.ACTION_PLAN || type == Type.SUGGESTION;
    }
    
    /**
     * Check if message is system message
     */
    public boolean isSystemMessage() {
        return type == Type.SYSTEM || type == Type.ERROR || type == Type.CONTEXT_UPDATE;
    }
    
    /**
     * Check if message has media content
     */
    public boolean hasMedia() {
        return mediaUrl != null && !mediaUrl.isEmpty();
    }
    
    /**
     * Check if message requires user interaction
     */
    public boolean requiresInteraction() {
        return isInteractive && (quickReplies != null || executionPlan != null || actionId != null);
    }
    
    /**
     * Get formatted timestamp
     */
    public String getFormattedTimestamp() {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault());
        return sdf.format(new java.util.Date(timestamp));
    }
    
    /**
     * Get relative timestamp (e.g., "2 minutes ago")
     */
    public String getRelativeTimestamp() {
        long now = System.currentTimeMillis();
        long diff = now - timestamp;
        
        if (diff < 60000) { // Less than 1 minute
            return "Just now";
        } else if (diff < 3600000) { // Less than 1 hour
            int minutes = (int) (diff / 60000);
            return minutes + " minute" + (minutes > 1 ? "s" : "") + " ago";
        } else if (diff < 86400000) { // Less than 1 day
            int hours = (int) (diff / 3600000);
            return hours + " hour" + (hours > 1 ? "s" : "") + " ago";
        } else {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("MMM dd", java.util.Locale.getDefault());
            return sdf.format(new java.util.Date(timestamp));
        }
    }
    
    /**
     * Get confidence level description
     */
    public String getConfidenceDescription() {
        if (confidence >= 0.9f) {
            return "Very confident";
        } else if (confidence >= 0.7f) {
            return "Confident";
        } else if (confidence >= 0.5f) {
            return "Somewhat confident";
        } else {
            return "Low confidence";
        }
    }
    
    /**
     * Generate unique message ID
     */
    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + hashCode();
    }
    
    /**
     * Clone message
     */
    public ConversationMessage clone() {
        ConversationMessage cloned = new ConversationMessage(content, type, timestamp);
        cloned.status = status;
        cloned.messageId = messageId;
        cloned.metadata = new HashMap<>(metadata);
        cloned.executionPlan = executionPlan;
        cloned.contextInfo = contextInfo;
        cloned.confidence = confidence;
        cloned.isProactive = isProactive;
        cloned.mediaUrl = mediaUrl;
        cloned.mediaType = mediaType;
        cloned.thumbnailUrl = thumbnailUrl;
        cloned.isInteractive = isInteractive;
        cloned.quickReplies = quickReplies != null ? quickReplies.clone() : null;
        cloned.actionId = actionId;
        return cloned;
    }
    
    @Override
    public String toString() {
        return String.format("ConversationMessage{type=%s, content='%s', timestamp=%d, confidence=%.2f}", 
            type, content, timestamp, confidence);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ConversationMessage that = (ConversationMessage) obj;
        return messageId.equals(that.messageId);
    }
    
    @Override
    public int hashCode() {
        return messageId.hashCode();
    }
}
