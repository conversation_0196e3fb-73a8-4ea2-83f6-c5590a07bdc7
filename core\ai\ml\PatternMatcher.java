package com.jarvis.core.ai.ml;

import android.util.Log;
import com.jarvis.core.ai.interfaces.IAiContextEngine;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Pattern Matcher for context analysis
 * Identifies and matches behavioral and contextual patterns
 */
public class PatternMatcher {
    
    private static final String TAG = "PatternMatcher";
    private static final int MIN_PATTERN_OCCURRENCES = 3;
    private static final float MIN_PATTERN_CONFIDENCE = 0.6f;
    private static final long PATTERN_LEARNING_WINDOW = 604800000; // 7 days
    
    // Learned patterns database
    private final Map<String, LearnedPattern> learnedPatterns = new ConcurrentHashMap<>();
    
    // Pattern templates for common behaviors
    private final List<PatternTemplate> patternTemplates = new ArrayList<>();
    
    // Pattern matching history
    private final List<PatternMatch> matchHistory = new ArrayList<>();
    
    public PatternMatcher() {
        initializePatternTemplates();
        Log.d(TAG, "Pattern Matcher initialized");
    }
    
    /**
     * Match patterns in current context
     */
    public List<ContextFusionEngine.ContextPattern> matchPatterns(
            Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context) {
        
        List<ContextFusionEngine.ContextPattern> matchedPatterns = new ArrayList<>();
        
        // Match against learned patterns
        List<ContextFusionEngine.ContextPattern> learnedMatches = matchLearnedPatterns(context);
        matchedPatterns.addAll(learnedMatches);
        
        // Match against pattern templates
        List<ContextFusionEngine.ContextPattern> templateMatches = matchPatternTemplates(context);
        matchedPatterns.addAll(templateMatches);
        
        // Match temporal patterns
        List<ContextFusionEngine.ContextPattern> temporalMatches = matchTemporalPatterns(context);
        matchedPatterns.addAll(temporalMatches);
        
        // Record matches for learning
        recordPatternMatches(context, matchedPatterns);
        
        // Sort by confidence
        matchedPatterns.sort((p1, p2) -> Float.compare(p2.confidence, p1.confidence));
        
        Log.d(TAG, "Matched " + matchedPatterns.size() + " patterns");
        return matchedPatterns;
    }
    
    /**
     * Learn new pattern from context sequence
     */
    public void learnPattern(List<Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData>> contextSequence) {
        if (contextSequence.size() < 2) return;
        
        // Extract pattern from sequence
        String patternKey = generatePatternKey(contextSequence);
        
        LearnedPattern pattern = learnedPatterns.computeIfAbsent(
            patternKey, k -> new LearnedPattern(k));
        
        pattern.addOccurrence(contextSequence);
        
        // Remove patterns with low confidence
        cleanupLowConfidencePatterns();
        
        Log.d(TAG, "Learned pattern: " + patternKey);
    }
    
    /**
     * Get pattern statistics
     */
    public PatternStatistics getPatternStatistics() {
        PatternStatistics stats = new PatternStatistics();
        
        stats.totalLearnedPatterns = learnedPatterns.size();
        stats.highConfidencePatterns = (int) learnedPatterns.values().stream()
            .filter(p -> p.confidence >= MIN_PATTERN_CONFIDENCE)
            .count();
        
        stats.patternsByType = new HashMap<>();
        for (LearnedPattern pattern : learnedPatterns.values()) {
            String type = pattern.getPatternType();
            stats.patternsByType.merge(type, 1, Integer::sum);
        }
        
        stats.recentMatches = matchHistory.size();
        
        return stats;
    }
    
    /**
     * Match against learned patterns
     */
    private List<ContextFusionEngine.ContextPattern> matchLearnedPatterns(
            Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context) {
        
        List<ContextFusionEngine.ContextPattern> matches = new ArrayList<>();
        
        for (LearnedPattern learnedPattern : learnedPatterns.values()) {
            if (learnedPattern.confidence < MIN_PATTERN_CONFIDENCE) continue;
            
            float matchScore = calculatePatternMatchScore(context, learnedPattern);
            
            if (matchScore > 0.7f) {
                ContextFusionEngine.ContextPattern pattern = new ContextFusionEngine.ContextPattern(
                    learnedPattern.patternId, learnedPattern.description);
                
                pattern.confidence = matchScore * learnedPattern.confidence;
                pattern.associatedIntent = learnedPattern.associatedIntent;
                pattern.urgencyIndicator = learnedPattern.urgencyIndicator;
                pattern.patternData.putAll(learnedPattern.patternData);
                
                matches.add(pattern);
            }
        }
        
        return matches;
    }
    
    /**
     * Match against pattern templates
     */
    private List<ContextFusionEngine.ContextPattern> matchPatternTemplates(
            Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context) {
        
        List<ContextFusionEngine.ContextPattern> matches = new ArrayList<>();
        
        for (PatternTemplate template : patternTemplates) {
            float matchScore = template.match(context);
            
            if (matchScore > 0.6f) {
                ContextFusionEngine.ContextPattern pattern = new ContextFusionEngine.ContextPattern(
                    template.templateId, template.description);
                
                pattern.confidence = matchScore;
                pattern.associatedIntent = template.associatedIntent;
                pattern.urgencyIndicator = template.urgencyIndicator;
                
                matches.add(pattern);
            }
        }
        
        return matches;
    }
    
    /**
     * Match temporal patterns
     */
    private List<ContextFusionEngine.ContextPattern> matchTemporalPatterns(
            Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context) {
        
        List<ContextFusionEngine.ContextPattern> matches = new ArrayList<>();
        
        Calendar cal = Calendar.getInstance();
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        
        // Morning routine pattern
        if (hour >= 6 && hour <= 9) {
            ContextFusionEngine.ContextPattern pattern = new ContextFusionEngine.ContextPattern(
                "morning_routine", "Morning routine pattern");
            pattern.confidence = 0.8f;
            pattern.associatedIntent = "productivity";
            pattern.urgencyIndicator = 0.3f;
            matches.add(pattern);
        }
        
        // Work hours pattern
        if (hour >= 9 && hour <= 17 && dayOfWeek >= Calendar.MONDAY && dayOfWeek <= Calendar.FRIDAY) {
            ContextFusionEngine.ContextPattern pattern = new ContextFusionEngine.ContextPattern(
                "work_hours", "Work hours pattern");
            pattern.confidence = 0.9f;
            pattern.associatedIntent = "work";
            pattern.urgencyIndicator = 0.5f;
            matches.add(pattern);
        }
        
        // Evening routine pattern
        if (hour >= 18 && hour <= 22) {
            ContextFusionEngine.ContextPattern pattern = new ContextFusionEngine.ContextPattern(
                "evening_routine", "Evening routine pattern");
            pattern.confidence = 0.7f;
            pattern.associatedIntent = "relaxation";
            pattern.urgencyIndicator = 0.1f;
            matches.add(pattern);
        }
        
        // Weekend pattern
        if (dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY) {
            ContextFusionEngine.ContextPattern pattern = new ContextFusionEngine.ContextPattern(
                "weekend", "Weekend pattern");
            pattern.confidence = 0.8f;
            pattern.associatedIntent = "leisure";
            pattern.urgencyIndicator = 0.2f;
            matches.add(pattern);
        }
        
        return matches;
    }
    
    /**
     * Calculate pattern match score
     */
    private float calculatePatternMatchScore(
            Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context,
            LearnedPattern learnedPattern) {
        
        float totalScore = 0.0f;
        int matchedConditions = 0;
        
        for (Map.Entry<IAiContextEngine.ContextType, Object> condition : 
             learnedPattern.conditions.entrySet()) {
            
            if (context.containsKey(condition.getKey())) {
                IAiContextEngine.ContextData contextData = context.get(condition.getKey());
                
                // Check if condition value matches context data
                if (contextData.data.containsValue(condition.getValue())) {
                    totalScore += 1.0f;
                    matchedConditions++;
                } else {
                    // Partial match based on similarity
                    float similarity = calculateValueSimilarity(condition.getValue(), contextData.data);
                    totalScore += similarity;
                    matchedConditions++;
                }
            }
        }
        
        // Add temporal match score
        float temporalScore = calculateTemporalMatchScore(learnedPattern);
        totalScore += temporalScore;
        matchedConditions++;
        
        return matchedConditions > 0 ? totalScore / matchedConditions : 0.0f;
    }
    
    /**
     * Calculate value similarity
     */
    private float calculateValueSimilarity(Object patternValue, Map<String, Object> contextData) {
        float maxSimilarity = 0.0f;
        
        for (Object contextValue : contextData.values()) {
            if (patternValue.equals(contextValue)) {
                return 1.0f; // Exact match
            }
            
            if (patternValue instanceof String && contextValue instanceof String) {
                String patternStr = (String) patternValue;
                String contextStr = (String) contextValue;
                
                // Simple string similarity
                float similarity = calculateStringSimilarity(patternStr, contextStr);
                maxSimilarity = Math.max(maxSimilarity, similarity);
            }
        }
        
        return maxSimilarity;
    }
    
    /**
     * Calculate string similarity
     */
    private float calculateStringSimilarity(String str1, String str2) {
        if (str1.equals(str2)) return 1.0f;
        if (str1.contains(str2) || str2.contains(str1)) return 0.7f;
        
        // Levenshtein distance based similarity
        int distance = calculateLevenshteinDistance(str1, str2);
        int maxLength = Math.max(str1.length(), str2.length());
        
        return maxLength > 0 ? 1.0f - (float) distance / maxLength : 0.0f;
    }
    
    /**
     * Calculate Levenshtein distance
     */
    private int calculateLevenshteinDistance(String str1, String str2) {
        int[][] dp = new int[str1.length() + 1][str2.length() + 1];
        
        for (int i = 0; i <= str1.length(); i++) {
            for (int j = 0; j <= str2.length(); j++) {
                if (i == 0) {
                    dp[i][j] = j;
                } else if (j == 0) {
                    dp[i][j] = i;
                } else {
                    dp[i][j] = Math.min(
                        dp[i - 1][j - 1] + (str1.charAt(i - 1) == str2.charAt(j - 1) ? 0 : 1),
                        Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1)
                    );
                }
            }
        }
        
        return dp[str1.length()][str2.length()];
    }
    
    /**
     * Calculate temporal match score
     */
    private float calculateTemporalMatchScore(LearnedPattern pattern) {
        Calendar cal = Calendar.getInstance();
        int currentHour = cal.get(Calendar.HOUR_OF_DAY);
        int currentDayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        
        // Check if current time matches pattern's typical occurrence times
        float hourMatch = 0.0f;
        float dayMatch = 0.0f;
        
        if (pattern.typicalHours.contains(currentHour)) {
            hourMatch = 1.0f;
        } else {
            // Find closest hour
            int closestHour = pattern.typicalHours.stream()
                .min(Comparator.comparingInt(h -> Math.abs(h - currentHour)))
                .orElse(currentHour);
            
            int hourDiff = Math.abs(closestHour - currentHour);
            hourMatch = Math.max(0.0f, 1.0f - hourDiff / 12.0f); // 12-hour decay
        }
        
        if (pattern.typicalDaysOfWeek.contains(currentDayOfWeek)) {
            dayMatch = 1.0f;
        } else {
            dayMatch = 0.3f; // Partial match for different day
        }
        
        return (hourMatch + dayMatch) / 2.0f;
    }
    
    /**
     * Record pattern matches for learning
     */
    private void recordPatternMatches(
            Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context,
            List<ContextFusionEngine.ContextPattern> matches) {
        
        for (ContextFusionEngine.ContextPattern pattern : matches) {
            PatternMatch match = new PatternMatch();
            match.patternId = pattern.patternId;
            match.timestamp = System.currentTimeMillis();
            match.confidence = pattern.confidence;
            match.context = new HashMap<>(context);
            
            matchHistory.add(match);
        }
        
        // Limit match history
        if (matchHistory.size() > 1000) {
            matchHistory.subList(0, matchHistory.size() - 1000).clear();
        }
    }
    
    /**
     * Generate pattern key from context sequence
     */
    private String generatePatternKey(
            List<Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData>> sequence) {
        
        StringBuilder key = new StringBuilder();
        
        for (Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context : sequence) {
            for (IAiContextEngine.ContextType type : context.keySet()) {
                key.append(type.name()).append("_");
            }
            key.append("|");
        }
        
        return key.toString();
    }
    
    /**
     * Initialize pattern templates
     */
    private void initializePatternTemplates() {
        // Communication pattern
        patternTemplates.add(new PatternTemplate("communication_burst", 
            "High communication activity") {
            @Override
            public float match(Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context) {
                if (context.containsKey(IAiContextEngine.ContextType.COMMUNICATION)) {
                    IAiContextEngine.ContextData commData = context.get(IAiContextEngine.ContextType.COMMUNICATION);
                    Integer messageCount = (Integer) commData.data.get("messageCount");
                    return messageCount != null && messageCount > 5 ? 0.9f : 0.0f;
                }
                return 0.0f;
            }
        });
        
        // Productivity pattern
        patternTemplates.add(new PatternTemplate("productivity_mode", 
            "Focused productivity session") {
            @Override
            public float match(Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context) {
                boolean hasProductivityApp = false;
                boolean lowNotifications = true;
                
                if (context.containsKey(IAiContextEngine.ContextType.USER_ACTIVITY)) {
                    IAiContextEngine.ContextData activityData = context.get(IAiContextEngine.ContextType.USER_ACTIVITY);
                    String currentApp = (String) activityData.data.get("currentApp");
                    hasProductivityApp = currentApp != null && 
                        (currentApp.contains("office") || currentApp.contains("document") || 
                         currentApp.contains("editor"));
                }
                
                if (context.containsKey(IAiContextEngine.ContextType.NOTIFICATIONS)) {
                    IAiContextEngine.ContextData notifData = context.get(IAiContextEngine.ContextType.NOTIFICATIONS);
                    Integer notifCount = (Integer) notifData.data.get("count");
                    lowNotifications = notifCount == null || notifCount < 3;
                }
                
                return (hasProductivityApp && lowNotifications) ? 0.8f : 0.0f;
            }
        });
    }
    
    /**
     * Clean up low confidence patterns
     */
    private void cleanupLowConfidencePatterns() {
        learnedPatterns.entrySet().removeIf(entry -> 
            entry.getValue().confidence < 0.3f && 
            entry.getValue().occurrences < MIN_PATTERN_OCCURRENCES);
    }
    
    /**
     * Learned pattern representation
     */
    private static class LearnedPattern {
        public final String patternId;
        public String description;
        public final Map<IAiContextEngine.ContextType, Object> conditions;
        public float confidence;
        public int occurrences;
        public String associatedIntent;
        public float urgencyIndicator;
        public final Map<String, Object> patternData;
        public final Set<Integer> typicalHours;
        public final Set<Integer> typicalDaysOfWeek;
        
        public LearnedPattern(String patternId) {
            this.patternId = patternId;
            this.conditions = new HashMap<>();
            this.confidence = 0.0f;
            this.occurrences = 0;
            this.patternData = new HashMap<>();
            this.typicalHours = new HashSet<>();
            this.typicalDaysOfWeek = new HashSet<>();
        }
        
        public void addOccurrence(List<Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData>> sequence) {
            occurrences++;
            
            // Update conditions from first context in sequence
            if (!sequence.isEmpty()) {
                Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> firstContext = sequence.get(0);
                
                for (Map.Entry<IAiContextEngine.ContextType, IAiContextEngine.ContextData> entry : 
                     firstContext.entrySet()) {
                    
                    Object primaryValue = entry.getValue().data.values().iterator().next();
                    conditions.put(entry.getKey(), primaryValue);
                }
                
                // Update temporal information
                Calendar cal = Calendar.getInstance();
                cal.setTimeInMillis(firstContext.values().iterator().next().timestamp);
                typicalHours.add(cal.get(Calendar.HOUR_OF_DAY));
                typicalDaysOfWeek.add(cal.get(Calendar.DAY_OF_WEEK));
            }
            
            // Update confidence
            confidence = Math.min(occurrences / 10.0f, 1.0f);
            
            // Generate description
            description = "Pattern with " + conditions.size() + " conditions";
        }
        
        public String getPatternType() {
            if (conditions.containsKey(IAiContextEngine.ContextType.COMMUNICATION)) {
                return "communication";
            } else if (conditions.containsKey(IAiContextEngine.ContextType.USER_ACTIVITY)) {
                return "activity";
            } else {
                return "general";
            }
        }
    }
    
    /**
     * Pattern template base class
     */
    private abstract static class PatternTemplate {
        public final String templateId;
        public final String description;
        public String associatedIntent;
        public float urgencyIndicator;
        
        public PatternTemplate(String templateId, String description) {
            this.templateId = templateId;
            this.description = description;
            this.urgencyIndicator = 0.0f;
        }
        
        public abstract float match(Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context);
    }
    
    /**
     * Pattern match record
     */
    private static class PatternMatch {
        public String patternId;
        public long timestamp;
        public float confidence;
        public Map<IAiContextEngine.ContextType, IAiContextEngine.ContextData> context;
    }
    
    /**
     * Pattern statistics
     */
    public static class PatternStatistics {
        public int totalLearnedPatterns;
        public int highConfidencePatterns;
        public Map<String, Integer> patternsByType;
        public int recentMatches;
    }
}
