package com.jarvis.api.gemini;

import android.util.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Real HTTP client for Gemini API integration
 * Handles actual HTTP communication with Google's Gemini API
 */
public class GeminiHttpClient {
    
    private static final String TAG = "GeminiHttpClient";
    private static final String BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/";
    private static final int TIMEOUT_MS = 30000;
    private static final int MAX_RETRIES = 3;
    private static final long RETRY_DELAY_MS = 1000;
    
    private final String apiKey;
    private final Executor executor;
    
    public GeminiHttpClient(String apiKey) {
        this.apiKey = apiKey;
        this.executor = Executors.newCachedThreadPool();
    }
    
    /**
     * Generate content using Gemini API
     */
    public CompletableFuture<GeminiResponse> generateContent(String model, String prompt, 
                                                           GenerationConfig config) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateContentSync(model, prompt, config);
            } catch (Exception e) {
                Log.e(TAG, "Error generating content", e);
                return createErrorResponse("Generation failed: " + e.getMessage());
            }
        }, executor);
    }
    
    /**
     * Generate content synchronously
     */
    private GeminiResponse generateContentSync(String model, String prompt, 
                                             GenerationConfig config) throws Exception {
        
        String url = BASE_URL + model + ":generateContent?key=" + apiKey;
        
        // Build request payload
        JSONObject requestBody = buildRequestPayload(prompt, config);
        
        // Make HTTP request with retries
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                String response = makeHttpRequest(url, requestBody.toString());
                return parseGeminiResponse(response);
                
            } catch (Exception e) {
                Log.w(TAG, "Attempt " + attempt + " failed: " + e.getMessage());
                
                if (attempt == MAX_RETRIES) {
                    throw e;
                }
                
                // Wait before retry
                Thread.sleep(RETRY_DELAY_MS * attempt);
            }
        }
        
        throw new Exception("All retry attempts failed");
    }
    
    /**
     * Build request payload for Gemini API
     */
    private JSONObject buildRequestPayload(String prompt, GenerationConfig config) throws JSONException {
        JSONObject payload = new JSONObject();
        
        // Add contents
        JSONArray contents = new JSONArray();
        JSONObject content = new JSONObject();
        JSONArray parts = new JSONArray();
        JSONObject part = new JSONObject();
        part.put("text", prompt);
        parts.put(part);
        content.put("parts", parts);
        contents.put(content);
        payload.put("contents", contents);
        
        // Add generation config
        if (config != null) {
            JSONObject generationConfig = new JSONObject();
            generationConfig.put("temperature", config.temperature);
            generationConfig.put("topK", config.topK);
            generationConfig.put("topP", config.topP);
            generationConfig.put("maxOutputTokens", config.maxOutputTokens);
            
            if (config.stopSequences != null && config.stopSequences.length > 0) {
                JSONArray stopSequences = new JSONArray();
                for (String stop : config.stopSequences) {
                    stopSequences.put(stop);
                }
                generationConfig.put("stopSequences", stopSequences);
            }
            
            payload.put("generationConfig", generationConfig);
        }
        
        // Add safety settings
        JSONArray safetySettings = new JSONArray();
        addSafetySetting(safetySettings, "HARM_CATEGORY_HARASSMENT", "BLOCK_MEDIUM_AND_ABOVE");
        addSafetySetting(safetySettings, "HARM_CATEGORY_HATE_SPEECH", "BLOCK_MEDIUM_AND_ABOVE");
        addSafetySetting(safetySettings, "HARM_CATEGORY_SEXUALLY_EXPLICIT", "BLOCK_MEDIUM_AND_ABOVE");
        addSafetySetting(safetySettings, "HARM_CATEGORY_DANGEROUS_CONTENT", "BLOCK_MEDIUM_AND_ABOVE");
        payload.put("safetySettings", safetySettings);
        
        return payload;
    }
    
    /**
     * Add safety setting to array
     */
    private void addSafetySetting(JSONArray safetySettings, String category, String threshold) 
            throws JSONException {
        JSONObject setting = new JSONObject();
        setting.put("category", category);
        setting.put("threshold", threshold);
        safetySettings.put(setting);
    }
    
    /**
     * Make HTTP request to Gemini API
     */
    private String makeHttpRequest(String urlString, String requestBody) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            // Configure connection
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "JarvisOS/1.0");
            connection.setDoOutput(true);
            connection.setConnectTimeout(TIMEOUT_MS);
            connection.setReadTimeout(TIMEOUT_MS);
            
            // Send request
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // Read response
            int responseCode = connection.getResponseCode();
            
            if (responseCode == HttpURLConnection.HTTP_OK) {
                return readResponse(connection.getInputStream());
            } else {
                String errorResponse = readResponse(connection.getErrorStream());
                throw new Exception("HTTP " + responseCode + ": " + errorResponse);
            }
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * Read response from input stream
     */
    private String readResponse(InputStream inputStream) throws IOException {
        if (inputStream == null) {
            return "";
        }
        
        StringBuilder response = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }
        
        return response.toString();
    }
    
    /**
     * Parse Gemini API response
     */
    private GeminiResponse parseGeminiResponse(String responseBody) throws JSONException {
        JSONObject jsonResponse = new JSONObject(responseBody);
        
        GeminiResponse response = new GeminiResponse();
        response.success = true;
        response.rawResponse = responseBody;
        
        // Extract candidates
        if (jsonResponse.has("candidates")) {
            JSONArray candidates = jsonResponse.getJSONArray("candidates");
            
            if (candidates.length() > 0) {
                JSONObject candidate = candidates.getJSONObject(0);
                
                // Extract content
                if (candidate.has("content")) {
                    JSONObject content = candidate.getJSONObject("content");
                    if (content.has("parts")) {
                        JSONArray parts = content.getJSONArray("parts");
                        if (parts.length() > 0) {
                            JSONObject part = parts.getJSONObject(0);
                            if (part.has("text")) {
                                response.generatedText = part.getString("text");
                            }
                        }
                    }
                }
                
                // Extract safety ratings
                if (candidate.has("safetyRatings")) {
                    response.safetyRatings = candidate.getJSONArray("safetyRatings");
                }
                
                // Extract finish reason
                if (candidate.has("finishReason")) {
                    response.finishReason = candidate.getString("finishReason");
                }
            }
        }
        
        // Extract usage metadata
        if (jsonResponse.has("usageMetadata")) {
            JSONObject usageMetadata = jsonResponse.getJSONObject("usageMetadata");
            response.promptTokenCount = usageMetadata.optInt("promptTokenCount", 0);
            response.candidatesTokenCount = usageMetadata.optInt("candidatesTokenCount", 0);
            response.totalTokenCount = usageMetadata.optInt("totalTokenCount", 0);
        }
        
        return response;
    }
    
    /**
     * Create error response
     */
    private GeminiResponse createErrorResponse(String errorMessage) {
        GeminiResponse response = new GeminiResponse();
        response.success = false;
        response.errorMessage = errorMessage;
        return response;
    }
    
    /**
     * Generation configuration for Gemini API
     */
    public static class GenerationConfig {
        public float temperature = 0.7f;
        public int topK = 40;
        public float topP = 0.95f;
        public int maxOutputTokens = 1024;
        public String[] stopSequences = null;
        
        public GenerationConfig() {}
        
        public GenerationConfig(float temperature, int topK, float topP, int maxOutputTokens) {
            this.temperature = temperature;
            this.topK = topK;
            this.topP = topP;
            this.maxOutputTokens = maxOutputTokens;
        }
        
        public GenerationConfig withStopSequences(String... sequences) {
            this.stopSequences = sequences;
            return this;
        }
        
        public static GenerationConfig creative() {
            return new GenerationConfig(0.9f, 40, 0.95f, 1024);
        }
        
        public static GenerationConfig balanced() {
            return new GenerationConfig(0.7f, 40, 0.95f, 1024);
        }
        
        public static GenerationConfig precise() {
            return new GenerationConfig(0.1f, 1, 0.1f, 1024);
        }
    }
    
    /**
     * Gemini API response structure
     */
    public static class GeminiResponse {
        public boolean success = false;
        public String generatedText = "";
        public String errorMessage = "";
        public String rawResponse = "";
        public String finishReason = "";
        public JSONArray safetyRatings = null;
        public int promptTokenCount = 0;
        public int candidatesTokenCount = 0;
        public int totalTokenCount = 0;
        
        public boolean isComplete() {
            return "STOP".equals(finishReason);
        }
        
        public boolean isSafe() {
            if (safetyRatings == null) return true;
            
            try {
                for (int i = 0; i < safetyRatings.length(); i++) {
                    JSONObject rating = safetyRatings.getJSONObject(i);
                    String probability = rating.optString("probability", "NEGLIGIBLE");
                    
                    if ("HIGH".equals(probability) || "MEDIUM".equals(probability)) {
                        return false;
                    }
                }
            } catch (JSONException e) {
                Log.e(TAG, "Error checking safety ratings", e);
                return false;
            }
            
            return true;
        }
    }
}
