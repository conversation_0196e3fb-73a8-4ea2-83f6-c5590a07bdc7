#!/bin/bash

# Jarvis OS Development Environment Setup Script
# This script sets up the development environment for building Jarvis OS

set -e

echo "🚀 Setting up Jarvis OS Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_success "macOS detected"
    else
        print_error "Unsupported operating system: $OSTYPE"
        print_error "Jarvis OS development requires Linux or macOS"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check available disk space (need at least 200GB for AOSP)
    if [[ "$OS" == "linux" ]]; then
        AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
        REQUIRED_SPACE=209715200 # 200GB in KB
    elif [[ "$OS" == "macos" ]]; then
        AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
        REQUIRED_SPACE=209715200 # 200GB in KB
    fi
    
    if [[ $AVAILABLE_SPACE -lt $REQUIRED_SPACE ]]; then
        print_error "Insufficient disk space. Need at least 200GB free."
        exit 1
    fi
    
    # Check RAM (need at least 16GB)
    if [[ "$OS" == "linux" ]]; then
        TOTAL_RAM=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        REQUIRED_RAM=16777216 # 16GB in KB
    elif [[ "$OS" == "macos" ]]; then
        TOTAL_RAM=$(sysctl -n hw.memsize)
        TOTAL_RAM=$((TOTAL_RAM / 1024)) # Convert to KB
        REQUIRED_RAM=16777216 # 16GB in KB
    fi
    
    if [[ $TOTAL_RAM -lt $REQUIRED_RAM ]]; then
        print_warning "Less than 16GB RAM detected. Build may be slow."
    fi
    
    print_success "System requirements check completed"
}

# Install required packages
install_packages() {
    print_status "Installing required packages..."
    
    if [[ "$OS" == "linux" ]]; then
        # Ubuntu/Debian packages
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y \
                git-core gnupg flex bison build-essential zip curl \
                zlib1g-dev gcc-multilib g++-multilib libc6-dev-i386 \
                libncurses5 lib32ncurses5-dev x11proto-core-dev libx11-dev \
                lib32z1-dev libgl1-mesa-dev libxml2-utils xsltproc unzip \
                fontconfig python3 python3-pip openjdk-11-jdk
        # CentOS/RHEL packages
        elif command -v yum &> /dev/null; then
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y \
                git gnupg flex bison glibc-devel.i686 glibc-devel \
                libstdc++.i686 ncurses-devel.i686 zlib-devel.i686 \
                python3 python3-pip java-11-openjdk-devel
        fi
    elif [[ "$OS" == "macos" ]]; then
        # macOS packages via Homebrew
        if ! command -v brew &> /dev/null; then
            print_status "Installing Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        fi
        
        brew install git gnupg coreutils findutils gnu-tar gnu-sed gawk \
                     python3 openjdk@11
        
        # Set JAVA_HOME for macOS
        echo 'export JAVA_HOME=$(/usr/libexec/java_home -v 11)' >> ~/.bashrc
    fi
    
    print_success "Required packages installed"
}

# Install Android SDK and tools
install_android_tools() {
    print_status "Installing Android SDK and tools..."
    
    # Create tools directory
    mkdir -p ~/android-tools
    cd ~/android-tools
    
    # Download Android SDK command line tools
    if [[ "$OS" == "linux" ]]; then
        SDK_URL="https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip"
    elif [[ "$OS" == "macos" ]]; then
        SDK_URL="https://dl.google.com/android/repository/commandlinetools-mac-9477386_latest.zip"
    fi
    
    if [[ ! -d "cmdline-tools" ]]; then
        print_status "Downloading Android SDK command line tools..."
        curl -o cmdline-tools.zip "$SDK_URL"
        unzip cmdline-tools.zip
        rm cmdline-tools.zip
    fi
    
    # Set up Android SDK environment
    export ANDROID_HOME=~/android-tools
    export PATH=$PATH:$ANDROID_HOME/cmdline-tools/bin:$ANDROID_HOME/platform-tools
    
    # Add to shell profile
    echo 'export ANDROID_HOME=~/android-tools' >> ~/.bashrc
    echo 'export PATH=$PATH:$ANDROID_HOME/cmdline-tools/bin:$ANDROID_HOME/platform-tools' >> ~/.bashrc
    
    # Accept licenses and install required packages
    yes | sdkmanager --licenses
    sdkmanager "platform-tools" "build-tools;33.0.0" "platforms;android-33"
    
    print_success "Android SDK and tools installed"
}

# Install repo tool
install_repo() {
    print_status "Installing repo tool..."
    
    mkdir -p ~/bin
    curl https://storage.googleapis.com/git-repo-downloads/repo > ~/bin/repo
    chmod a+x ~/bin/repo
    
    # Add to PATH
    echo 'export PATH=~/bin:$PATH' >> ~/.bashrc
    export PATH=~/bin:$PATH
    
    print_success "Repo tool installed"
}

# Set up Git configuration
setup_git() {
    print_status "Setting up Git configuration..."
    
    # Check if Git is already configured
    if ! git config --global user.name &> /dev/null; then
        echo "Please enter your Git configuration:"
        read -p "Your Name: " git_name
        read -p "Your Email: " git_email
        
        git config --global user.name "$git_name"
        git config --global user.email "$git_email"
    fi
    
    # Set up Git for large repositories
    git config --global core.preloadindex true
    git config --global core.fscache true
    git config --global gc.auto 256
    
    print_success "Git configuration completed"
}

# Create project directories
create_directories() {
    print_status "Creating project directories..."
    
    # Go back to project root
    cd "$(dirname "$0")/.."
    
    # Create necessary directories
    mkdir -p {build,out,logs,tools,docs/api,tests/unit,tests/integration}
    
    # Create build configuration
    cat > build/build.properties << EOF
# Jarvis OS Build Configuration
project.name=JarvisOS
project.version=1.0.0-alpha
target.sdk.version=33
min.sdk.version=28
compile.sdk.version=33

# Build settings
build.type=debug
enable.proguard=false
enable.multidex=true

# AI Services
ai.services.enabled=true
gemini.api.enabled=true
privacy.manager.enabled=true
EOF
    
    print_success "Project directories created"
}

# Set up development tools
setup_dev_tools() {
    print_status "Setting up development tools..."
    
    # Install Python dependencies
    pip3 install --user \
        requests \
        beautifulsoup4 \
        lxml \
        pyyaml \
        jinja2
    
    # Create development scripts
    cat > scripts/build.sh << 'EOF'
#!/bin/bash
# Jarvis OS Build Script
echo "Building Jarvis OS..."
# Build implementation will be added here
EOF
    
    cat > scripts/test.sh << 'EOF'
#!/bin/bash
# Jarvis OS Test Script
echo "Running Jarvis OS tests..."
# Test implementation will be added here
EOF
    
    cat > scripts/deploy.sh << 'EOF'
#!/bin/bash
# Jarvis OS Deploy Script
echo "Deploying Jarvis OS..."
# Deploy implementation will be added here
EOF
    
    chmod +x scripts/*.sh
    
    print_success "Development tools set up"
}

# Create IDE configuration
setup_ide() {
    print_status "Setting up IDE configuration..."
    
    # Create Android Studio / IntelliJ configuration
    mkdir -p .idea
    
    cat > .idea/jarvis-os.iml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://\$MODULE_DIR\$">
      <sourceFolder url="file://\$MODULE_DIR\$/core" isTestSource="false" />
      <sourceFolder url="file://\$MODULE_DIR\$/framework" isTestSource="false" />
      <sourceFolder url="file://\$MODULE_DIR\$/systemui" isTestSource="false" />
      <sourceFolder url="file://\$MODULE_DIR\$/api" isTestSource="false" />
      <sourceFolder url="file://\$MODULE_DIR\$/security" isTestSource="false" />
      <sourceFolder url="file://\$MODULE_DIR\$/tests" isTestSource="true" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>
EOF
    
    # Create VS Code configuration
    mkdir -p .vscode
    
    cat > .vscode/settings.json << EOF
{
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.compile.nullAnalysis.mode": "automatic",
    "files.exclude": {
        "**/build/": true,
        "**/out/": true,
        "**/.gradle/": true
    },
    "search.exclude": {
        "**/build/": true,
        "**/out/": true,
        "**/.gradle/": true
    }
}
EOF
    
    print_success "IDE configuration created"
}

# Main setup function
main() {
    echo "🤖 Jarvis OS Development Environment Setup"
    echo "=========================================="
    
    check_os
    check_requirements
    install_packages
    install_android_tools
    install_repo
    setup_git
    create_directories
    setup_dev_tools
    setup_ide
    
    echo ""
    print_success "🎉 Jarvis OS development environment setup completed!"
    echo ""
    echo "Next steps:"
    echo "1. Restart your terminal or run: source ~/.bashrc"
    echo "2. Run: ./scripts/build.sh to build the project"
    echo "3. Run: ./scripts/test.sh to run tests"
    echo ""
    echo "For more information, see docs/DEVELOPMENT.md"
}

# Run main function
main "$@"
