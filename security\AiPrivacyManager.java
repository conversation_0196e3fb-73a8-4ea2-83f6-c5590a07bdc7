package com.jarvis.security;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import com.jarvis.core.ai.interfaces.IAiContextEngine;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * AI Privacy Manager
 * Manages privacy controls, data anonymization, and security for AI services
 */
public class AiPrivacyManager {
    
    private static final String TAG = "AiPrivacyManager";
    private static final String PREFS_NAME = "jarvis_privacy_prefs";
    private static final String ENCRYPTION_ALGORITHM = "AES";
    private static final String HASH_ALGORITHM = "SHA-256";
    
    /**
     * Privacy levels for different types of data
     */
    public enum PrivacyLevel {
        PUBLIC(0),          // No privacy protection needed
        INTERNAL(1),        // Internal to device only
        SENSITIVE(2),       // Requires anonymization
        CONFIDENTIAL(3),    // Requires encryption
        RESTRICTED(4);      // Blocked from AI processing
        
        public final int level;
        
        PrivacyLevel(int level) {
            this.level = level;
        }
    }
    
    /**
     * Data categories for privacy classification
     */
    public enum DataCategory {
        PERSONAL_IDENTITY,      // Name, ID numbers, etc.
        LOCATION,              // GPS, addresses, etc.
        COMMUNICATION,         // Messages, calls, emails
        FINANCIAL,             // Payment info, banking
        HEALTH,                // Health data, medical info
        BIOMETRIC,             // Fingerprints, face data
        BEHAVIORAL,            // Usage patterns, preferences
        DEVICE_INFO,           // Device specs, settings
        APP_DATA,              // App-specific data
        SYSTEM_LOGS            // System and error logs
    }
    
    /**
     * Privacy audit log entry
     */
    public static class PrivacyAuditEntry {
        public long timestamp;
        public String component;
        public DataCategory dataCategory;
        public String action;
        public PrivacyLevel privacyLevel;
        public boolean userConsent;
        public String purpose;
        
        public PrivacyAuditEntry(String component, DataCategory category, String action) {
            this.timestamp = System.currentTimeMillis();
            this.component = component;
            this.dataCategory = category;
            this.action = action;
            this.userConsent = false;
        }
    }
    
    private static AiPrivacyManager instance;
    private Context context;
    private SharedPreferences preferences;
    private SecretKey encryptionKey;
    
    // Privacy settings
    private final Map<DataCategory, PrivacyLevel> privacyLevels = new ConcurrentHashMap<>();
    private final Map<String, Boolean> componentPermissions = new ConcurrentHashMap<>();
    private final Set<String> blockedDataTypes = new HashSet<>();
    
    // Audit logging
    private final Map<String, PrivacyAuditEntry> auditLog = new ConcurrentHashMap<>();
    private boolean auditingEnabled = true;
    
    /**
     * Get singleton instance
     */
    public static synchronized AiPrivacyManager getInstance(Context context) {
        if (instance == null) {
            instance = new AiPrivacyManager(context.getApplicationContext());
        }
        return instance;
    }
    
    private AiPrivacyManager(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        
        initializeEncryption();
        loadPrivacySettings();
        setupDefaultPrivacyLevels();
    }
    
    /**
     * Initialize encryption for sensitive data
     */
    private void initializeEncryption() {
        try {
            // In production, this would use Android Keystore
            KeyGenerator keyGen = KeyGenerator.getInstance(ENCRYPTION_ALGORITHM);
            keyGen.init(256);
            encryptionKey = keyGen.generateKey();
            
            Log.d(TAG, "Encryption initialized");
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize encryption", e);
        }
    }
    
    /**
     * Load privacy settings from preferences
     */
    private void loadPrivacySettings() {
        // Load privacy levels
        for (DataCategory category : DataCategory.values()) {
            String key = "privacy_level_" + category.name();
            int level = preferences.getInt(key, PrivacyLevel.SENSITIVE.level);
            privacyLevels.put(category, PrivacyLevel.values()[level]);
        }
        
        // Load component permissions
        Set<String> permissionKeys = preferences.getStringSet("component_permissions", new HashSet<>());
        for (String key : permissionKeys) {
            boolean permitted = preferences.getBoolean("perm_" + key, false);
            componentPermissions.put(key, permitted);
        }
        
        // Load blocked data types
        blockedDataTypes.addAll(preferences.getStringSet("blocked_data_types", new HashSet<>()));
        
        auditingEnabled = preferences.getBoolean("auditing_enabled", true);
        
        Log.d(TAG, "Privacy settings loaded");
    }
    
    /**
     * Save privacy settings to preferences
     */
    private void savePrivacySettings() {
        SharedPreferences.Editor editor = preferences.edit();
        
        // Save privacy levels
        for (Map.Entry<DataCategory, PrivacyLevel> entry : privacyLevels.entrySet()) {
            String key = "privacy_level_" + entry.getKey().name();
            editor.putInt(key, entry.getValue().level);
        }
        
        // Save component permissions
        Set<String> permissionKeys = new HashSet<>(componentPermissions.keySet());
        editor.putStringSet("component_permissions", permissionKeys);
        for (Map.Entry<String, Boolean> entry : componentPermissions.entrySet()) {
            editor.putBoolean("perm_" + entry.getKey(), entry.getValue());
        }
        
        // Save blocked data types
        editor.putStringSet("blocked_data_types", blockedDataTypes);
        
        editor.putBoolean("auditing_enabled", auditingEnabled);
        
        editor.apply();
        Log.d(TAG, "Privacy settings saved");
    }
    
    /**
     * Setup default privacy levels
     */
    private void setupDefaultPrivacyLevels() {
        // Set conservative defaults
        privacyLevels.putIfAbsent(DataCategory.PERSONAL_IDENTITY, PrivacyLevel.CONFIDENTIAL);
        privacyLevels.putIfAbsent(DataCategory.LOCATION, PrivacyLevel.SENSITIVE);
        privacyLevels.putIfAbsent(DataCategory.COMMUNICATION, PrivacyLevel.SENSITIVE);
        privacyLevels.putIfAbsent(DataCategory.FINANCIAL, PrivacyLevel.RESTRICTED);
        privacyLevels.putIfAbsent(DataCategory.HEALTH, PrivacyLevel.CONFIDENTIAL);
        privacyLevels.putIfAbsent(DataCategory.BIOMETRIC, PrivacyLevel.RESTRICTED);
        privacyLevels.putIfAbsent(DataCategory.BEHAVIORAL, PrivacyLevel.INTERNAL);
        privacyLevels.putIfAbsent(DataCategory.DEVICE_INFO, PrivacyLevel.INTERNAL);
        privacyLevels.putIfAbsent(DataCategory.APP_DATA, PrivacyLevel.SENSITIVE);
        privacyLevels.putIfAbsent(DataCategory.SYSTEM_LOGS, PrivacyLevel.INTERNAL);
    }
    
    /**
     * Check if data access is permitted for component
     */
    public boolean isDataAccessPermitted(String component, DataCategory category) {
        // Check if component has permission
        if (!componentPermissions.getOrDefault(component, false)) {
            logPrivacyAction(component, category, "ACCESS_DENIED_NO_PERMISSION");
            return false;
        }
        
        // Check if data category is blocked
        if (blockedDataTypes.contains(category.name())) {
            logPrivacyAction(component, category, "ACCESS_DENIED_BLOCKED");
            return false;
        }
        
        // Check privacy level
        PrivacyLevel level = privacyLevels.get(category);
        if (level == PrivacyLevel.RESTRICTED) {
            logPrivacyAction(component, category, "ACCESS_DENIED_RESTRICTED");
            return false;
        }
        
        logPrivacyAction(component, category, "ACCESS_GRANTED");
        return true;
    }
    
    /**
     * Process data according to privacy level
     */
    public Object processDataForPrivacy(Object data, DataCategory category, String component) {
        if (!isDataAccessPermitted(component, category)) {
            return null;
        }
        
        PrivacyLevel level = privacyLevels.get(category);
        
        switch (level) {
            case PUBLIC:
            case INTERNAL:
                return data;
                
            case SENSITIVE:
                return anonymizeData(data, category);
                
            case CONFIDENTIAL:
                return encryptData(data);
                
            case RESTRICTED:
            default:
                return null;
        }
    }
    
    /**
     * Anonymize data based on category
     */
    private Object anonymizeData(Object data, DataCategory category) {
        if (data == null) return null;
        
        String dataStr = data.toString();
        
        switch (category) {
            case PERSONAL_IDENTITY:
                return hashData(dataStr);
                
            case LOCATION:
                return generalizeLocation(dataStr);
                
            case COMMUNICATION:
                return sanitizeCommunication(dataStr);
                
            default:
                return data;
        }
    }
    
    /**
     * Encrypt sensitive data
     */
    private String encryptData(Object data) {
        if (data == null || encryptionKey == null) return null;
        
        try {
            Cipher cipher = Cipher.getInstance(ENCRYPTION_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, encryptionKey);
            
            byte[] encrypted = cipher.doFinal(data.toString().getBytes());
            return android.util.Base64.encodeToString(encrypted, android.util.Base64.DEFAULT);
        } catch (Exception e) {
            Log.e(TAG, "Encryption failed", e);
            return null;
        }
    }
    
    /**
     * Decrypt data
     */
    public String decryptData(String encryptedData) {
        if (encryptedData == null || encryptionKey == null) return null;
        
        try {
            Cipher cipher = Cipher.getInstance(ENCRYPTION_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, encryptionKey);
            
            byte[] encrypted = android.util.Base64.decode(encryptedData, android.util.Base64.DEFAULT);
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted);
        } catch (Exception e) {
            Log.e(TAG, "Decryption failed", e);
            return null;
        }
    }
    
    /**
     * Hash data for anonymization
     */
    private String hashData(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);
            byte[] hash = digest.digest(data.getBytes());
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            Log.e(TAG, "Hashing failed", e);
            return "HASHED_DATA";
        }
    }
    
    /**
     * Generalize location data
     */
    private String generalizeLocation(String location) {
        // Simple generalization - in production would use proper geo-generalization
        if (location.contains(",")) {
            String[] parts = location.split(",");
            if (parts.length >= 2) {
                try {
                    double lat = Double.parseDouble(parts[0].trim());
                    double lon = Double.parseDouble(parts[1].trim());
                    
                    // Round to ~1km precision
                    lat = Math.round(lat * 100.0) / 100.0;
                    lon = Math.round(lon * 100.0) / 100.0;
                    
                    return lat + "," + lon;
                } catch (NumberFormatException e) {
                    return "GENERALIZED_LOCATION";
                }
            }
        }
        return "GENERALIZED_LOCATION";
    }
    
    /**
     * Sanitize communication data
     */
    private String sanitizeCommunication(String communication) {
        // Remove personal identifiers, phone numbers, emails
        return communication
            .replaceAll("\\b\\d{3}-\\d{3}-\\d{4}\\b", "[PHONE]")
            .replaceAll("\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b", "[EMAIL]")
            .replaceAll("\\b[A-Z][a-z]+ [A-Z][a-z]+\\b", "[NAME]");
    }
    
    /**
     * Set privacy level for data category
     */
    public void setPrivacyLevel(DataCategory category, PrivacyLevel level) {
        privacyLevels.put(category, level);
        savePrivacySettings();
        
        logPrivacyAction("SYSTEM", category, "PRIVACY_LEVEL_CHANGED_TO_" + level.name());
        Log.d(TAG, "Privacy level set: " + category + " -> " + level);
    }
    
    /**
     * Grant permission to component
     */
    public void grantComponentPermission(String component, boolean granted) {
        componentPermissions.put(component, granted);
        savePrivacySettings();
        
        String action = granted ? "PERMISSION_GRANTED" : "PERMISSION_REVOKED";
        logPrivacyAction(component, null, action);
        Log.d(TAG, "Component permission: " + component + " -> " + granted);
    }
    
    /**
     * Block/unblock data type
     */
    public void setDataTypeBlocked(DataCategory category, boolean blocked) {
        if (blocked) {
            blockedDataTypes.add(category.name());
        } else {
            blockedDataTypes.remove(category.name());
        }
        savePrivacySettings();
        
        String action = blocked ? "DATA_TYPE_BLOCKED" : "DATA_TYPE_UNBLOCKED";
        logPrivacyAction("SYSTEM", category, action);
        Log.d(TAG, "Data type block status: " + category + " -> " + blocked);
    }
    
    /**
     * Get privacy audit log
     */
    public Map<String, PrivacyAuditEntry> getAuditLog() {
        return new HashMap<>(auditLog);
    }
    
    /**
     * Clear audit log
     */
    public void clearAuditLog() {
        auditLog.clear();
        Log.d(TAG, "Audit log cleared");
    }
    
    /**
     * Log privacy action
     */
    private void logPrivacyAction(String component, DataCategory category, String action) {
        if (!auditingEnabled) return;
        
        PrivacyAuditEntry entry = new PrivacyAuditEntry(component, category, action);
        String key = entry.timestamp + "_" + component + "_" + action;
        auditLog.put(key, entry);
        
        // Limit audit log size
        if (auditLog.size() > 10000) {
            // Remove oldest entries
            auditLog.entrySet().removeIf(e -> 
                e.getValue().timestamp < System.currentTimeMillis() - 86400000); // 24 hours
        }
    }
    
    /**
     * Export privacy data for user
     */
    public Map<String, Object> exportPrivacyData() {
        Map<String, Object> export = new HashMap<>();
        export.put("privacyLevels", new HashMap<>(privacyLevels));
        export.put("componentPermissions", new HashMap<>(componentPermissions));
        export.put("blockedDataTypes", new HashSet<>(blockedDataTypes));
        export.put("auditLog", new HashMap<>(auditLog));
        export.put("exportTimestamp", System.currentTimeMillis());
        
        return export;
    }
}
