package com.jarvis.core.ai.interfaces;

import java.util.*;

/**
 * Interface for AI Personalization Service
 * Manages on-device learning, user profiles, and preference storage
 */
public interface IAiPersonalization {
    
    /**
     * User preference categories
     */
    enum PreferenceCategory {
        COMMUNICATION,      // Communication style and preferences
        PRODUCTIVITY,       // Work habits and productivity patterns
        ENTERTAINMENT,      // Entertainment preferences
        TRAVEL,            // Travel and navigation preferences
        SHOPPING,          // Shopping habits and preferences
        HEALTH,            // Health and fitness preferences
        PRIVACY,           // Privacy and security preferences
        ACCESSIBILITY,     // Accessibility needs and preferences
        GENERAL            // General behavioral preferences
    }
    
    /**
     * Learning confidence levels
     */
    enum ConfidenceLevel {
        LOW(0.0f, 0.3f),
        MEDIUM(0.3f, 0.7f),
        HIGH(0.7f, 1.0f);
        
        public final float min;
        public final float max;
        
        ConfidenceLevel(float min, float max) {
            this.min = min;
            this.max = max;
        }
    }
    
    /**
     * User preference data structure
     */
    class UserPreference {
        public String key;
        public PreferenceCategory category;
        public Object value;
        public float confidence;
        public long lastUpdated;
        public int usageCount;
        public String source;
        
        public UserPreference(String key, PreferenceCategory category, Object value) {
            this.key = key;
            this.category = category;
            this.value = value;
            this.confidence = 0.5f;
            this.lastUpdated = System.currentTimeMillis();
            this.usageCount = 1;
            this.source = "system";
        }
    }
    
    /**
     * User behavioral pattern
     */
    class BehavioralPattern {
        public String patternId;
        public String description;
        public Map<String, Object> conditions;
        public Map<String, Object> actions;
        public float frequency;
        public List<String> timePatterns;
        public ConfidenceLevel confidence;
        
        public BehavioralPattern(String description) {
            this.patternId = generatePatternId();
            this.description = description;
            this.conditions = new HashMap<>();
            this.actions = new HashMap<>();
            this.frequency = 0.0f;
            this.timePatterns = new ArrayList<>();
            this.confidence = ConfidenceLevel.LOW;
        }
        
        private String generatePatternId() {
            return "pattern_" + System.currentTimeMillis() + "_" + hashCode();
        }
    }
    
    /**
     * User profile containing all personalization data
     */
    class UserProfile {
        public String userId;
        public Map<PreferenceCategory, List<UserPreference>> preferences;
        public List<BehavioralPattern> patterns;
        public Map<String, Object> demographics;
        public long createdTimestamp;
        public long lastUpdated;
        
        public UserProfile(String userId) {
            this.userId = userId;
            this.preferences = new HashMap<>();
            this.patterns = new ArrayList<>();
            this.demographics = new HashMap<>();
            this.createdTimestamp = System.currentTimeMillis();
            this.lastUpdated = this.createdTimestamp;
        }
    }
    
    /**
     * Get user profile
     * @param userId User identifier
     * @return User profile or null if not found
     */
    UserProfile getUserProfile(String userId);
    
    /**
     * Update user preference
     * @param userId User identifier
     * @param preference Preference to update
     */
    void updatePreference(String userId, UserPreference preference);
    
    /**
     * Get user preferences by category
     * @param userId User identifier
     * @param category Preference category
     * @return List of preferences in category
     */
    List<UserPreference> getPreferences(String userId, PreferenceCategory category);
    
    /**
     * Learn from user behavior
     * @param userId User identifier
     * @param action User action taken
     * @param context Context when action was taken
     */
    void learnFromBehavior(String userId, String action, Map<String, Object> context);
    
    /**
     * Get behavioral patterns
     * @param userId User identifier
     * @param minConfidence Minimum confidence level
     * @return List of behavioral patterns
     */
    List<BehavioralPattern> getBehavioralPatterns(String userId, ConfidenceLevel minConfidence);
    
    /**
     * Predict user preference for given context
     * @param userId User identifier
     * @param context Current context
     * @param category Preference category
     * @return Predicted preference value
     */
    Object predictPreference(String userId, Map<String, Object> context, PreferenceCategory category);
    
    /**
     * Get personalized suggestions
     * @param userId User identifier
     * @param context Current context
     * @param maxSuggestions Maximum number of suggestions
     * @return List of personalized suggestions
     */
    List<String> getPersonalizedSuggestions(String userId, Map<String, Object> context, int maxSuggestions);
    
    /**
     * Export user data (for privacy compliance)
     * @param userId User identifier
     * @return Exported user data
     */
    Map<String, Object> exportUserData(String userId);
    
    /**
     * Delete user data (for privacy compliance)
     * @param userId User identifier
     * @param category Category to delete (null for all)
     */
    void deleteUserData(String userId, PreferenceCategory category);
    
    /**
     * Get learning statistics
     * @param userId User identifier
     * @return Learning statistics
     */
    Map<String, Object> getLearningStatistics(String userId);
    
    /**
     * Set learning enabled/disabled
     * @param userId User identifier
     * @param category Category to control
     * @param enabled Whether learning is enabled
     */
    void setLearningEnabled(String userId, PreferenceCategory category, boolean enabled);
    
    /**
     * Check if learning is enabled for category
     * @param userId User identifier
     * @param category Category to check
     * @return true if learning is enabled
     */
    boolean isLearningEnabled(String userId, PreferenceCategory category);
}
