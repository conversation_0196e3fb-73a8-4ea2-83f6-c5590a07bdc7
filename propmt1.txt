Prompt for AI-Assisted AOSP Modification for Deep AI Automation (Project "Jarvis OS")

1. Project Vision & Core Objective:

Goal: To create a modified Android OS ("Jarvis OS") by deeply integrating advanced AI automation capabilities directly into the AOSP framework. This OS should provide users with proactive, context-aware, and highly capable assistance that significantly surpasses current mobile assistants like Google Assistant or Siri.
Core AI Engine: The system will leverage a powerful future Gemini model API (e.g., capabilities significantly beyond current Gemini 1.5 Pro, referred to as "Gemini Advanced API" for this project) for complex reasoning, planning, and natural language understanding, complemented by on-device AI for speed, privacy, and offline tasks.
2. Foundation & Scope:

Base: Android Open Source Project (AOSP) – specify target version (e.g., latest stable).
Scope: Modifications will span from framework services and system UI to potentially HALs and core libraries, focusing on enabling seamless AI orchestration of tasks on behalf of the user.
3. Key AI-Driven OS Capabilities to Implement:

a. Hyper-Contextual Awareness:
OS-level service to securely and continuously gather, fuse, and interpret context from:
User activity (current app, screen content via secure OS-level analysis, input methods).
App states and inter-app intents.
Notifications (content, source, actions).
Device sensors (location, motion, ambient environment, proximity).
Communication channels (e.g., summaries or intents from calls/messages, with explicit user consent and privacy safeguards).
User's calendar, contacts, and other PIM data (with granular permissions).
Learned user routines and preferences.
b. Advanced Task Planning & Orchestration:
Ability to understand complex, multi-step natural language goals from the user.
The OS AI Core, with the help of the Gemini Advanced API, will break down these goals into a sequence of executable actions across different apps and system functions.
Manage dependencies and conditional logic within these action plans.
c. Proactive & Predictive Automation:
Anticipate user needs based on context, learned patterns, and real-time events.
Proactively suggest actions, automate routine tasks, or prepare information before the user explicitly asks.
Examples: "Based on your calendar and traffic, you should leave for your meeting in 15 minutes. Shall I brief you on the meeting agenda and start navigation?"
d. Seamless Conversational Interface:
A system-wide, always-available conversational interface (voice and text) deeply integrated into the System UI.
Ability to maintain conversational context across tasks and apps.
e. Personalized On-Device Learning:
Continuously and privately learn user preferences, habits, and communication styles to tailor assistance.
Prioritize on-device learning for privacy, with mechanisms for secure, aggregated model improvements if applicable.
4. Target AOSP Modification Areas & New AI Components:

a. Framework Services (frameworks/base/services/core/java/com/android/server/):
Modify Existing: ActivityManagerService, WindowManagerService, NotificationManagerService, PowerManagerService, ConnectivityService to expose richer context to, and accept fine-grained control from, the AI Core.
New AI System Services:
AiContextEngineService: Manages collection, fusion, and interpretation of system-wide context. Provides context to other AI services and authorized apps.
AiPlanningOrchestrationService: Interfaces with the Gemini Advanced API for complex task planning. Manages execution of multi-step action plans, error handling, and user feedback loops.
AiPersonalizationService: Manages on-device learning models, user profiles, and preference storage.
b. System UI (frameworks/base/packages/SystemUI/):
Integrate the primary "Jarvis" conversational interface.
Develop UI elements for proactive AI suggestions, AI activity transparency, and user overrides.
c. HALs (hardware/):
Investigate if new or extended HALs are needed for more direct/efficient access to sensor data or specialized AI co-processors.
d. Core Native Libraries & Runtimes (system/core/, system/libai/ (new dir)):
Develop native libraries for efficient on-device model inference (e.g., optimized for Gemini Nano-class models), secure data handling, and inter-process communication for AI services.
e. Settings Application (packages/apps/Settings/):
Create a comprehensive "AI Assistant Settings" section for:
Granular permission controls for AI context access and actions.
Customization of AI behavior, proactivity levels, and personalities.
Transparency logs of AI decisions and actions.
Data management and privacy controls.
5. Gemini Advanced API Integration Strategy:

a. Decision Logic for Cloud Invocation: Define criteria for when the on-device AI Core escalates a task/query to the Gemini Advanced API (e.g., complexity of natural language, need for external knowledge, multi-step planning beyond on-device capabilities).
b. Secure API Communication: Design secure and efficient communication protocols between the on-device AI Core and the Gemini Advanced API endpoints.
c. Prompt Engineering for OS Control:
Develop dynamic system prompt templates that provide the Gemini Advanced API with:
Concise but comprehensive current device/user context from AiContextEngineService.
The specific user query or goal.
A clearly defined list of "available OS actions/capabilities" that the OS can execute (e.g., {"action": "openApp", "packageName": "...", "extras": "..."}, {"action": "setSystemSetting", "setting": "wifi", "value": "on"}, {"action": "composeEmail", "to": "...", "subject": "...", "body_hint": "..."}).
Instructions for the desired output format (e.g., a JSON array representing a plan of executable OS actions with parameters).
d. Handling LLM Responses: Logic to parse the structured response from Gemini, validate the proposed plan, and dispatch actions to AiPlanningOrchestrationService. Include error handling for malformed or unsafe suggestions.
6. Example Advanced Automation Scenarios to Enable:

(Scenario 1 - Proactive Meeting Prep): "User has a meeting titled 'Project Phoenix Sync' in 30 minutes with '<EMAIL>'. Jarvis OS should: 1. Check for recent emails from Jane Doe or with 'Project Phoenix' in the subject. 2. Summarize key points from these emails. 3. Check the primary document associated with 'Project Phoenix' (if known/linked) for recent changes. 4. Check traffic to the meeting location (if in calendar). 5. 10 minutes before, offer to brief the user on the summary, recent doc changes, and ETA."
(Scenario 2 - Intelligent Content Creation Workflow): User says, "Jarvis, draft an email to the marketing team about the new 'Summer Campaign' performance. Pull the key metrics from the 'Sales Dashboard' app (assume it has an API or can be screen-read securely by OS AI), summarize the attached 'Campaign Report PDF', and suggest three next steps based on the findings."
(Scenario 3 - Adaptive Evening Routine): "Based on user's typical evening behavior (e.g., dims smart lights, opens a specific streaming app, silences notifications after 9 PM), if the user hasn't initiated these by 9:15 PM and is detected to be home and not actively engaged in a call/video, Jarvis OS should proactively ask: 'It's getting late. Shall I start your evening wind-down routine?'"
7. Security & Privacy by Design:

Principle of Least Privilege: AI components should only have access to the data and system functions absolutely necessary for their tasks.
On-Device Processing First: Maximize on-device processing for sensitive data and routine tasks.
Data Minimization & Anonymization: When cloud interaction is necessary, send only the minimum required context, anonymized where possible.
Secure Enclaves/Trusted Execution Environments: For storing cryptographic keys, sensitive user preferences, or running critical AI model components.
Transparent AI Activity & User Overrides: Clear logs of AI actions and easy ways for users to understand, correct, or disable AI behaviors.
Robust Permission Model: Granular and understandable permissions for all AI capabilities.
8. Requested Output (from the AI Development Assistant):

a. Detailed Architectural Document: Outlining the modified AOSP structure, new AI services, their interactions, data flows, and APIs.
b. Identification of AOSP Modules for Modification: List of specific AOSP Java/C++ files/classes to be modified, with a high-level description of the required changes for each.
c. Interface Definitions (e.g., AIDL for new services): For AiContextEngineService, AiPlanningOrchestrationService, etc.
d. Skeleton Code / Pseudocode: For the core logic of the new AI services and key modification points in existing AOSP services.
e. Example System Prompts & Expected JSON Schemas: For interaction between the on-device AI Core and the Gemini Advanced API for the specified scenarios.
f. Security and Privacy Design Proposal: Addressing the key challenges outlined.
g. Development Roadmap & Prioritization Suggestions: High-level phases for tackling this project.