package com.jarvis.systemui.jarvis.conversation;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.*;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.jarvis.core.ai.interfaces.IAiPlanningOrchestration;
import com.jarvis.systemui.jarvis.voice.VoiceInputManager;
import java.util.*;

/**
 * Advanced Conversational Interface for Jarvis
 * Provides rich, interactive conversation experience with voice and visual elements
 */
public class JarvisConversationView extends LinearLayout {
    
    private static final String TAG = "JarvisConversationView";
    private static final int MAX_CONVERSATION_HISTORY = 100;
    private static final long TYPING_ANIMATION_DURATION = 1500;
    
    // UI Components
    private RecyclerView conversationRecyclerView;
    private EditText messageInput;
    private ImageButton sendButton;
    private ImageButton voiceButton;
    private ImageButton attachButton;
    private View typingIndicator;
    private TextView contextDisplay;
    private LinearLayout quickActionsContainer;
    private ProgressBar processingIndicator;
    
    // Conversation management
    private ConversationAdapter conversationAdapter;
    private List<ConversationMessage> conversationHistory;
    private VoiceInputManager voiceInputManager;
    
    // Listeners
    private ConversationListener conversationListener;
    
    // State
    private boolean isVoiceInputActive = false;
    private boolean isProcessing = false;
    private String currentContext = "";
    
    public JarvisConversationView(Context context) {
        super(context);
        init(context);
    }
    
    public JarvisConversationView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    /**
     * Initialize the conversation view
     */
    private void init(Context context) {
        setOrientation(VERTICAL);
        
        // Inflate layout
        LayoutInflater.from(context).inflate(R.layout.jarvis_conversation_view, this, true);
        
        // Initialize components
        initializeViews();
        setupConversationRecyclerView();
        setupInputControls();
        setupVoiceInput();
        setupQuickActions();
        
        // Initialize conversation history
        conversationHistory = new ArrayList<>();
        
        Log.d(TAG, "Jarvis Conversation View initialized");
    }
    
    /**
     * Initialize view components
     */
    private void initializeViews() {
        conversationRecyclerView = findViewById(R.id.conversation_recycler_view);
        messageInput = findViewById(R.id.message_input);
        sendButton = findViewById(R.id.send_button);
        voiceButton = findViewById(R.id.voice_button);
        attachButton = findViewById(R.id.attach_button);
        typingIndicator = findViewById(R.id.typing_indicator);
        contextDisplay = findViewById(R.id.context_display);
        quickActionsContainer = findViewById(R.id.quick_actions_container);
        processingIndicator = findViewById(R.id.processing_indicator);
        
        // Initially hide typing indicator
        typingIndicator.setVisibility(GONE);
        processingIndicator.setVisibility(GONE);
    }
    
    /**
     * Setup conversation RecyclerView
     */
    private void setupConversationRecyclerView() {
        conversationAdapter = new ConversationAdapter(conversationHistory);
        conversationRecyclerView.setAdapter(conversationAdapter);
        conversationRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        
        // Auto-scroll to bottom when new messages are added
        conversationAdapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
            @Override
            public void onItemInserted(int positionStart) {
                conversationRecyclerView.smoothScrollToPosition(conversationAdapter.getItemCount() - 1);
            }
        });
    }
    
    /**
     * Setup input controls
     */
    private void setupInputControls() {
        sendButton.setOnClickListener(v -> sendMessage());
        
        messageInput.setOnEditorActionListener((v, actionId, event) -> {
            sendMessage();
            return true;
        });
        
        // Enable/disable send button based on input
        messageInput.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                sendButton.setEnabled(s.length() > 0);
                updateSendButtonAppearance();
            }
        });
        
        attachButton.setOnClickListener(v -> showAttachmentOptions());
    }
    
    /**
     * Setup voice input
     */
    private void setupVoiceInput() {
        voiceInputManager = new VoiceInputManager(getContext());
        voiceInputManager.setVoiceInputListener(new VoiceInputListener());
        
        voiceButton.setOnClickListener(v -> toggleVoiceInput());
        voiceButton.setOnLongClickListener(v -> {
            startContinuousVoiceInput();
            return true;
        });
    }
    
    /**
     * Setup quick actions
     */
    private void setupQuickActions() {
        addQuickAction("What can you do?", "help");
        addQuickAction("Show my schedule", "calendar");
        addQuickAction("Check weather", "weather");
        addQuickAction("Set reminder", "reminder");
        addQuickAction("Open settings", "settings");
    }
    
    /**
     * Add quick action button
     */
    private void addQuickAction(String text, String action) {
        Button quickActionButton = new Button(getContext());
        quickActionButton.setText(text);
        quickActionButton.setBackgroundResource(R.drawable.quick_action_button_background);
        quickActionButton.setOnClickListener(v -> executeQuickAction(action, text));
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(8, 4, 8, 4);
        quickActionButton.setLayoutParams(params);
        
        quickActionsContainer.addView(quickActionButton);
    }
    
    /**
     * Send message
     */
    private void sendMessage() {
        String message = messageInput.getText().toString().trim();
        if (message.isEmpty()) return;
        
        // Add user message to conversation
        addUserMessage(message);
        
        // Clear input
        messageInput.setText("");
        
        // Show processing state
        showProcessing(true);
        
        // Notify listener
        if (conversationListener != null) {
            conversationListener.onUserInput(message);
        }
    }
    
    /**
     * Add user message to conversation
     */
    public void addUserMessage(String message) {
        ConversationMessage userMessage = new ConversationMessage(
            message, ConversationMessage.Type.USER, System.currentTimeMillis());
        
        conversationHistory.add(userMessage);
        conversationAdapter.notifyItemInserted(conversationHistory.size() - 1);
        
        // Limit conversation history
        if (conversationHistory.size() > MAX_CONVERSATION_HISTORY) {
            conversationHistory.remove(0);
            conversationAdapter.notifyItemRemoved(0);
        }
    }
    
    /**
     * Add assistant message to conversation
     */
    public void addAssistantMessage(String message) {
        showProcessing(false);
        
        ConversationMessage assistantMessage = new ConversationMessage(
            message, ConversationMessage.Type.ASSISTANT, System.currentTimeMillis());
        
        // Show typing animation
        showTypingIndicator(true);
        
        // Simulate typing delay
        postDelayed(() -> {
            showTypingIndicator(false);
            
            conversationHistory.add(assistantMessage);
            conversationAdapter.notifyItemInserted(conversationHistory.size() - 1);
            
            // Limit conversation history
            if (conversationHistory.size() > MAX_CONVERSATION_HISTORY) {
                conversationHistory.remove(0);
                conversationAdapter.notifyItemRemoved(0);
            }
        }, TYPING_ANIMATION_DURATION);
    }
    
    /**
     * Show action plan in conversation
     */
    public void showActionPlan(IAiPlanningOrchestration.ExecutionPlan plan) {
        showProcessing(false);
        
        StringBuilder planText = new StringBuilder();
        planText.append("📋 I'll help you with that. Here's my plan:\n\n");
        
        for (int i = 0; i < plan.actions.size(); i++) {
            IAiPlanningOrchestration.PlanAction action = plan.actions.get(i);
            planText.append(String.format("%d. %s\n", i + 1, action.description));
        }
        
        planText.append(String.format("\nEstimated time: %d seconds", plan.estimatedDuration / 1000));
        
        ConversationMessage planMessage = new ConversationMessage(
            planText.toString(), ConversationMessage.Type.ACTION_PLAN, System.currentTimeMillis());
        planMessage.executionPlan = plan;
        
        conversationHistory.add(planMessage);
        conversationAdapter.notifyItemInserted(conversationHistory.size() - 1);
    }
    
    /**
     * Show error message
     */
    public void showError(String error) {
        showProcessing(false);
        
        ConversationMessage errorMessage = new ConversationMessage(
            "❌ " + error, ConversationMessage.Type.ERROR, System.currentTimeMillis());
        
        conversationHistory.add(errorMessage);
        conversationAdapter.notifyItemInserted(conversationHistory.size() - 1);
    }
    
    /**
     * Start voice input
     */
    public void startVoiceInput() {
        if (!isVoiceInputActive) {
            toggleVoiceInput();
        }
    }
    
    /**
     * Toggle voice input
     */
    private void toggleVoiceInput() {
        if (isVoiceInputActive) {
            stopVoiceInput();
        } else {
            startVoiceInputInternal();
        }
    }
    
    /**
     * Start voice input internal
     */
    private void startVoiceInputInternal() {
        isVoiceInputActive = true;
        updateVoiceButtonAppearance();
        
        voiceInputManager.startListening();
        
        if (conversationListener != null) {
            conversationListener.onVoiceInputStarted();
        }
    }
    
    /**
     * Stop voice input
     */
    private void stopVoiceInput() {
        isVoiceInputActive = false;
        updateVoiceButtonAppearance();
        
        voiceInputManager.stopListening();
    }
    
    /**
     * Start continuous voice input
     */
    private void startContinuousVoiceInput() {
        // Implementation for continuous voice conversation
        startVoiceInputInternal();
        // TODO: Implement continuous conversation mode
    }
    
    /**
     * Execute quick action
     */
    private void executeQuickAction(String action, String text) {
        addUserMessage(text);
        
        if (conversationListener != null) {
            conversationListener.onUserInput(text);
        }
    }
    
    /**
     * Show attachment options
     */
    private void showAttachmentOptions() {
        // TODO: Implement attachment options (images, files, etc.)
        Toast.makeText(getContext(), "Attachment options coming soon", Toast.LENGTH_SHORT).show();
    }
    
    /**
     * Show/hide processing indicator
     */
    private void showProcessing(boolean show) {
        isProcessing = show;
        processingIndicator.setVisibility(show ? VISIBLE : GONE);
    }
    
    /**
     * Show/hide typing indicator
     */
    private void showTypingIndicator(boolean show) {
        if (show) {
            typingIndicator.setVisibility(VISIBLE);
            startTypingAnimation();
        } else {
            typingIndicator.setVisibility(GONE);
        }
    }
    
    /**
     * Start typing animation
     */
    private void startTypingAnimation() {
        // Simple fade in/out animation for typing indicator
        ValueAnimator animator = ValueAnimator.ofFloat(0.3f, 1.0f);
        animator.setDuration(800);
        animator.setRepeatCount(ValueAnimator.INFINITE);
        animator.setRepeatMode(ValueAnimator.REVERSE);
        
        animator.addUpdateListener(animation -> {
            float alpha = (Float) animation.getAnimatedValue();
            typingIndicator.setAlpha(alpha);
        });
        
        animator.start();
    }
    
    /**
     * Update send button appearance
     */
    private void updateSendButtonAppearance() {
        boolean hasText = messageInput.getText().length() > 0;
        sendButton.setAlpha(hasText ? 1.0f : 0.5f);
    }
    
    /**
     * Update voice button appearance
     */
    private void updateVoiceButtonAppearance() {
        if (isVoiceInputActive) {
            voiceButton.setImageResource(R.drawable.ic_mic_active);
            // Add pulsing animation
            startVoicePulseAnimation();
        } else {
            voiceButton.setImageResource(R.drawable.ic_mic_inactive);
            voiceButton.clearAnimation();
        }
    }
    
    /**
     * Start voice pulse animation
     */
    private void startVoicePulseAnimation() {
        ValueAnimator pulseAnimator = ValueAnimator.ofFloat(1.0f, 1.2f);
        pulseAnimator.setDuration(1000);
        pulseAnimator.setRepeatCount(ValueAnimator.INFINITE);
        pulseAnimator.setRepeatMode(ValueAnimator.REVERSE);
        
        pulseAnimator.addUpdateListener(animation -> {
            float scale = (Float) animation.getAnimatedValue();
            voiceButton.setScaleX(scale);
            voiceButton.setScaleY(scale);
        });
        
        pulseAnimator.start();
    }
    
    /**
     * Update context display
     */
    public void updateContext(String context) {
        currentContext = context;
        contextDisplay.setText("Context: " + context);
        contextDisplay.setVisibility(context.isEmpty() ? GONE : VISIBLE);
    }
    
    /**
     * Set conversation listener
     */
    public void setConversationListener(ConversationListener listener) {
        this.conversationListener = listener;
    }
    
    /**
     * Voice input listener implementation
     */
    private class VoiceInputListener implements VoiceInputManager.VoiceInputListener {
        @Override
        public void onVoiceInputStarted() {
            // Voice input started
        }
        
        @Override
        public void onVoiceInputResult(String transcript) {
            stopVoiceInput();
            
            if (!transcript.isEmpty()) {
                messageInput.setText(transcript);
                sendMessage();
            }
            
            if (conversationListener != null) {
                conversationListener.onVoiceInputCompleted(transcript);
            }
        }
        
        @Override
        public void onVoiceInputError(String error) {
            stopVoiceInput();
            showError("Voice input error: " + error);
        }
    }
    
    /**
     * Conversation listener interface
     */
    public interface ConversationListener {
        void onUserInput(String input);
        void onVoiceInputStarted();
        void onVoiceInputCompleted(String transcript);
    }
    
    /**
     * Simple text watcher for input monitoring
     */
    private abstract static class SimpleTextWatcher implements android.text.TextWatcher {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
        
        @Override
        public void afterTextChanged(android.text.Editable s) {}
    }
}
