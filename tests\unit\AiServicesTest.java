package com.jarvis.tests.unit;

import org.junit.Before;
import org.junit.Test;
import org.junit.After;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.content.Context;
import com.jarvis.core.ai.interfaces.*;
import com.jarvis.core.ai.services.*;
import com.jarvis.api.gemini.GeminiApiClient;
import com.jarvis.security.AiPrivacyManager;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Unit tests for AI Services
 */
public class AiServicesTest {
    
    private Context mockContext;
    private AiContextEngineService contextEngine;
    private AiPlanningOrchestrationService planningService;
    private AiPersonalizationService personalizationService;
    private GeminiApiClient geminiClient;
    private AiPrivacyManager privacyManager;
    
    @Before
    public void setUp() {
        // Mock Android context
        mockContext = mock(Context.class);
        
        // Initialize services
        contextEngine = new AiContextEngineService();
        planningService = new AiPlanningOrchestrationService();
        personalizationService = new AiPersonalizationService();
        
        // Initialize API client
        geminiClient = new GeminiApiClient("test-api-key", "gemini-pro");
        
        // Initialize privacy manager
        privacyManager = AiPrivacyManager.getInstance(mockContext);
    }
    
    @After
    public void tearDown() {
        // Cleanup resources
        if (contextEngine != null) {
            contextEngine.onDestroy();
        }
        if (planningService != null) {
            planningService.onDestroy();
        }
        if (personalizationService != null) {
            personalizationService.onDestroy();
        }
    }
    
    @Test
    public void testContextEngineBasicFunctionality() {
        // Test context data creation
        IAiContextEngine.ContextData contextData = new IAiContextEngine.ContextData(
            IAiContextEngine.ContextType.USER_ACTIVITY, 
            createTestContextMap()
        );
        
        assertNotNull("Context data should not be null", contextData);
        assertEquals("Context type should match", 
            IAiContextEngine.ContextType.USER_ACTIVITY, contextData.type);
        assertNotNull("Context data map should not be null", contextData.data);
        assertTrue("Timestamp should be recent", 
            contextData.timestamp > System.currentTimeMillis() - 1000);
    }
    
    @Test
    public void testContextPermissions() {
        // Test default permissions (should be false for privacy)
        assertFalse("Default permission should be false", 
            contextEngine.isContextPermitted(IAiContextEngine.ContextType.PERSONAL_DATA));
        
        // Test enabling permission
        contextEngine.setContextEnabled(IAiContextEngine.ContextType.USER_ACTIVITY, true);
        assertTrue("Permission should be enabled", 
            contextEngine.isContextPermitted(IAiContextEngine.ContextType.USER_ACTIVITY));
        
        // Test disabling permission
        contextEngine.setContextEnabled(IAiContextEngine.ContextType.USER_ACTIVITY, false);
        assertFalse("Permission should be disabled", 
            contextEngine.isContextPermitted(IAiContextEngine.ContextType.USER_ACTIVITY));
    }
    
    @Test
    public void testContextDataUpdate() {
        // Enable context collection
        contextEngine.setContextEnabled(IAiContextEngine.ContextType.USER_ACTIVITY, true);
        
        // Create and update context data
        IAiContextEngine.ContextData contextData = new IAiContextEngine.ContextData(
            IAiContextEngine.ContextType.USER_ACTIVITY, 
            createTestContextMap()
        );
        
        contextEngine.updateContext(contextData);
        
        // Verify context was stored
        List<IAiContextEngine.ContextData> history = contextEngine.getHistoricalContext(
            IAiContextEngine.ContextType.USER_ACTIVITY, 60000); // Last minute
        
        assertFalse("History should not be empty", history.isEmpty());
        assertEquals("Should have one context entry", 1, history.size());
        assertEquals("Context type should match", 
            IAiContextEngine.ContextType.USER_ACTIVITY, history.get(0).type);
    }
    
    @Test
    public void testPlanningServiceBasicFunctionality() {
        // Create a simple execution plan
        IAiContextEngine.FusedContext context = new IAiContextEngine.FusedContext();
        context.currentActivity = "test_activity";
        context.userIntent = "test_intent";
        
        IAiPlanningOrchestration.ExecutionPlan plan = planningService.createPlan(
            "open settings app", context);
        
        assertNotNull("Plan should not be null", plan);
        assertNotNull("Plan ID should not be null", plan.planId);
        assertEquals("User query should match", "open settings app", plan.userQuery);
        assertFalse("Plan should have actions", plan.actions.isEmpty());
        assertEquals("Plan status should be pending", 
            IAiPlanningOrchestration.ExecutionStatus.PENDING, plan.overallStatus);
    }
    
    @Test
    public void testPlanActionValidation() {
        // Test valid action
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("packageName", "com.android.settings");
        
        IAiPlanningOrchestration.PlanAction validAction = 
            new IAiPlanningOrchestration.PlanAction(
                IAiPlanningOrchestration.ActionType.OPEN_APP, parameters);
        
        assertTrue("Valid action should pass validation", 
            planningService.validateAction(validAction));
        
        // Test invalid action
        IAiPlanningOrchestration.PlanAction invalidAction = 
            new IAiPlanningOrchestration.PlanAction(null, null);
        
        assertFalse("Invalid action should fail validation", 
            planningService.validateAction(invalidAction));
    }
    
    @Test
    public void testPersonalizationServiceBasicFunctionality() {
        String testUserId = "test_user_123";
        
        // Test user profile creation
        IAiPersonalization.UserProfile profile = personalizationService.getUserProfile(testUserId);
        // Profile might be null initially, so we'll create one through preference update
        
        // Test preference update
        IAiPersonalization.UserPreference preference = 
            new IAiPersonalization.UserPreference(
                "test_preference", 
                IAiPersonalization.PreferenceCategory.PRODUCTIVITY, 
                "test_value"
            );
        
        personalizationService.updatePreference(testUserId, preference);
        
        // Verify preference was stored
        List<IAiPersonalization.UserPreference> preferences = 
            personalizationService.getPreferences(testUserId, 
                IAiPersonalization.PreferenceCategory.PRODUCTIVITY);
        
        assertFalse("Preferences should not be empty", preferences.isEmpty());
        assertEquals("Should have one preference", 1, preferences.size());
        assertEquals("Preference key should match", "test_preference", 
            preferences.get(0).key);
    }
    
    @Test
    public void testBehaviorLearning() {
        String testUserId = "test_user_456";
        
        // Test behavior learning
        Map<String, Object> context = new HashMap<>();
        context.put("timeOfDay", "morning");
        context.put("dayOfWeek", "monday");
        
        personalizationService.learnFromBehavior(testUserId, "app_opened", context);
        
        // Verify behavioral patterns were created
        List<IAiPersonalization.BehavioralPattern> patterns = 
            personalizationService.getBehavioralPatterns(testUserId, 
                IAiPersonalization.ConfidenceLevel.LOW);
        
        assertFalse("Patterns should not be empty", patterns.isEmpty());
    }
    
    @Test
    public void testPersonalizedSuggestions() {
        String testUserId = "test_user_789";
        
        // Create some preferences and patterns first
        IAiPersonalization.UserPreference preference = 
            new IAiPersonalization.UserPreference(
                "preferred_app", 
                IAiPersonalization.PreferenceCategory.PRODUCTIVITY, 
                "com.android.settings"
            );
        preference.confidence = 0.8f;
        
        personalizationService.updatePreference(testUserId, preference);
        
        // Test suggestions
        Map<String, Object> context = new HashMap<>();
        context.put("timeOfDay", "morning");
        
        List<String> suggestions = personalizationService.getPersonalizedSuggestions(
            testUserId, context, 5);
        
        assertNotNull("Suggestions should not be null", suggestions);
        assertFalse("Should have suggestions", suggestions.isEmpty());
        assertTrue("Should not exceed max suggestions", suggestions.size() <= 5);
    }
    
    @Test
    public void testGeminiApiClientInitialization() {
        // Test API client initialization
        CompletableFuture<Boolean> initResult = geminiClient.initialize();
        
        assertNotNull("Initialization result should not be null", initResult);
        
        // Wait for initialization (with timeout)
        try {
            Boolean result = initResult.get(5, java.util.concurrent.TimeUnit.SECONDS);
            assertTrue("Initialization should succeed", result);
        } catch (Exception e) {
            fail("Initialization should not throw exception: " + e.getMessage());
        }
    }
    
    @Test
    public void testGeminiApiTaskPlanning() {
        // Initialize client first
        geminiClient.initialize().join();
        
        // Create task planning request
        IAiContextEngine.FusedContext context = new IAiContextEngine.FusedContext();
        context.currentActivity = "home_screen";
        
        GeminiApiClient.TaskPlanningRequest request = 
            new GeminiApiClient.TaskPlanningRequest("open settings", context);
        
        // Test task planning
        CompletableFuture<GeminiApiClient.GeminiResponse> responseFuture = 
            geminiClient.planTasks(request);
        
        assertNotNull("Response future should not be null", responseFuture);

        try {
            GeminiApiClient.GeminiResponse response = responseFuture.get(10,
                java.util.concurrent.TimeUnit.SECONDS);
            
            assertNotNull("Response should not be null", response);
            assertTrue("Response should be successful", response.success);
            assertEquals("Response type should match", 
                GeminiApiClient.RequestType.TASK_PLANNING, response.type);
        } catch (Exception e) {
            fail("Task planning should not throw exception: " + e.getMessage());
        }
    }
    
    @Test
    public void testPrivacyManagerBasicFunctionality() {
        // Test default privacy levels
        assertNotNull("Privacy manager should not be null", privacyManager);
        
        // Test data access permission (should be false by default)
        boolean permitted = privacyManager.isDataAccessPermitted(
            "test_component", 
            AiPrivacyManager.DataCategory.PERSONAL_IDENTITY
        );
        assertFalse("Access should be denied by default", permitted);
        
        // Grant permission and test again
        privacyManager.grantComponentPermission("test_component", true);
        // Note: Still might be denied due to privacy level, which is correct
    }
    
    @Test
    public void testPrivacyDataProcessing() {
        // Test data processing with different privacy levels
        String testData = "sensitive_user_data";
        
        // Test with restricted data (should return null)
        Object processedData = privacyManager.processDataForPrivacy(
            testData, 
            AiPrivacyManager.DataCategory.FINANCIAL, 
            "test_component"
        );
        assertNull("Restricted data should return null", processedData);
        
        // Test with public data (should return original)
        privacyManager.setPrivacyLevel(
            AiPrivacyManager.DataCategory.SYSTEM_LOGS, 
            AiPrivacyManager.PrivacyLevel.PUBLIC
        );
        privacyManager.grantComponentPermission("test_component", true);
        
        Object publicData = privacyManager.processDataForPrivacy(
            testData, 
            AiPrivacyManager.DataCategory.SYSTEM_LOGS, 
            "test_component"
        );
        assertEquals("Public data should return original", testData, publicData);
    }
    
    @Test
    public void testIntegrationBetweenServices() {
        // Test integration between context engine and planning service
        contextEngine.setContextEnabled(IAiContextEngine.ContextType.USER_ACTIVITY, true);
        
        // Create context data
        IAiContextEngine.ContextData contextData = new IAiContextEngine.ContextData(
            IAiContextEngine.ContextType.USER_ACTIVITY, 
            createTestContextMap()
        );
        contextEngine.updateContext(contextData);
        
        // Get current context
        IAiContextEngine.FusedContext fusedContext = contextEngine.getCurrentContext();
        assertNotNull("Fused context should not be null", fusedContext);
        
        // Use context in planning service
        IAiPlanningOrchestration.ExecutionPlan plan = planningService.createPlan(
            "test query", fusedContext);
        
        assertNotNull("Plan should be created successfully", plan);
        assertFalse("Plan should have actions", plan.actions.isEmpty());
    }
    
    /**
     * Helper method to create test context data
     */
    private Map<String, Object> createTestContextMap() {
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put("currentApp", "com.android.launcher");
        contextMap.put("screenState", "on");
        contextMap.put("batteryLevel", 75);
        contextMap.put("timestamp", System.currentTimeMillis());
        return contextMap;
    }
}
