package com.jarvis.core.ai.interfaces;

import android.content.Context;
import android.os.Bundle;
import java.util.*;

/**
 * Interface for the AI Context Engine Service
 * Manages collection, fusion, and interpretation of system-wide context
 */
public interface IAiContextEngine {
    
    /**
     * Context types that can be collected
     */
    enum ContextType {
        USER_ACTIVITY,      // Current app, screen content, input methods
        APP_STATES,         // App states and inter-app intents
        NOTIFICATIONS,      // Notification content, source, actions
        DEVICE_SENSORS,     // Location, motion, ambient environment
        COMMUNICATION,      // Calls/messages summaries (with consent)
        PERSONAL_DATA,      // Calendar, contacts, PIM data
        USER_ROUTINES       // Learned patterns and preferences
    }
    
    /**
     * Context data structure
     */
    class ContextData {
        public ContextType type;
        public long timestamp;
        public Map<String, Object> data;
        public float confidence;
        public String source;
        
        public ContextData(ContextType type, Map<String, Object> data) {
            this.type = type;
            this.data = data;
            this.timestamp = System.currentTimeMillis();
            this.confidence = 1.0f;
        }
    }
    
    /**
     * Fused context representing current user/device state with ML insights
     */
    class FusedContext {
        public Map<ContextType, ContextData> contexts;
        public String currentActivity;
        public String userIntent;
        public float urgencyLevel;
        public List<String> availableActions;

        // Enhanced ML insights (Phase 2)
        public List<Object> contextPatterns;
        public Object predictedContext;
        public float confidenceScore;

        public FusedContext() {
            this.contexts = new HashMap<>();
            this.urgencyLevel = 0.0f;
            this.availableActions = new ArrayList<>();
            this.contextPatterns = new ArrayList<>();
            this.confidenceScore = 0.0f;
        }
    }
    
    /**
     * Register for context updates
     * @param callback Callback to receive context updates
     * @param contextTypes Types of context to monitor
     */
    void registerContextListener(IContextListener callback, List<ContextType> contextTypes);
    
    /**
     * Unregister context listener
     * @param callback Callback to unregister
     */
    void unregisterContextListener(IContextListener callback);
    
    /**
     * Get current fused context
     * @return Current context state
     */
    FusedContext getCurrentContext();
    
    /**
     * Get historical context data
     * @param contextType Type of context to retrieve
     * @param timeRange Time range for historical data
     * @return List of historical context data
     */
    List<ContextData> getHistoricalContext(ContextType contextType, long timeRange);
    
    /**
     * Update context data
     * @param contextData New context data to add
     */
    void updateContext(ContextData contextData);
    
    /**
     * Check if context collection is permitted for given type
     * @param contextType Type of context to check
     * @return true if permitted, false otherwise
     */
    boolean isContextPermitted(ContextType contextType);
    
    /**
     * Enable/disable context collection for specific type
     * @param contextType Type of context
     * @param enabled Whether to enable collection
     */
    void setContextEnabled(ContextType contextType, boolean enabled);
    
    /**
     * Get context collection permissions
     * @return Map of context types and their permission status
     */
    Map<ContextType, Boolean> getContextPermissions();
    
    /**
     * Clear context data
     * @param contextType Type of context to clear (null for all)
     * @param olderThan Clear data older than this timestamp (0 for all)
     */
    void clearContextData(ContextType contextType, long olderThan);
    
    /**
     * Interface for context update callbacks
     */
    interface IContextListener {
        void onContextUpdated(ContextType type, ContextData data);
        void onFusedContextUpdated(FusedContext context);
    }
}
