package com.jarvis.core.ai.proactive;

import android.util.Log;
import com.jarvis.core.ai.interfaces.IAiContextEngine;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Automation Rule Engine
 * Manages and evaluates automation rules for proactive assistance
 */
public class AutomationRuleEngine {
    
    private static final String TAG = "AutomationRuleEngine";
    
    // Registered automation rules
    private final List<AutomationRule> rules = new CopyOnWriteArrayList<>();
    
    // Rule evaluation cache
    private final Map<String, CachedEvaluation> evaluationCache = new HashMap<>();
    private static final long CACHE_VALIDITY = 60000; // 1 minute
    
    public AutomationRuleEngine() {
        Log.d(TAG, "Automation Rule Engine initialized");
    }
    
    /**
     * Add automation rule
     */
    public void addRule(AutomationRule rule) {
        rules.add(rule);
        Log.d(TAG, "Added automation rule: " + rule.ruleId);
    }
    
    /**
     * Remove automation rule
     */
    public void removeRule(String ruleId) {
        rules.removeIf(rule -> rule.ruleId.equals(ruleId));
        evaluationCache.remove(ruleId);
        Log.d(TAG, "Removed automation rule: " + ruleId);
    }
    
    /**
     * Generate automation suggestions based on rules
     */
    public List<ProactiveAutomationEngine.AutomationSuggestion> generateSuggestions(
            IAiContextEngine.FusedContext context) {
        
        List<ProactiveAutomationEngine.AutomationSuggestion> suggestions = new ArrayList<>();
        
        for (AutomationRule rule : rules) {
            try {
                // Check cache first
                CachedEvaluation cached = evaluationCache.get(rule.ruleId);
                if (cached != null && !cached.isExpired()) {
                    if (cached.suggestion != null) {
                        suggestions.add(cached.suggestion);
                    }
                    continue;
                }
                
                // Evaluate rule
                ProactiveAutomationEngine.AutomationSuggestion suggestion = rule.evaluate(context);
                
                // Cache result
                evaluationCache.put(rule.ruleId, new CachedEvaluation(suggestion));
                
                if (suggestion != null) {
                    suggestions.add(suggestion);
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error evaluating rule: " + rule.ruleId, e);
            }
        }
        
        return suggestions;
    }
    
    /**
     * Get rule statistics
     */
    public RuleStatistics getRuleStatistics() {
        RuleStatistics stats = new RuleStatistics();
        stats.totalRules = rules.size();
        stats.activeRules = (int) rules.stream().filter(rule -> rule.enabled).count();
        stats.rulesByCategory = new HashMap<>();
        
        for (AutomationRule rule : rules) {
            stats.rulesByCategory.merge(rule.category, 1, Integer::sum);
        }
        
        return stats;
    }
    
    /**
     * Clear evaluation cache
     */
    public void clearCache() {
        evaluationCache.clear();
        Log.d(TAG, "Rule evaluation cache cleared");
    }
    
    /**
     * Cached evaluation result
     */
    private static class CachedEvaluation {
        public final ProactiveAutomationEngine.AutomationSuggestion suggestion;
        public final long timestamp;
        
        public CachedEvaluation(ProactiveAutomationEngine.AutomationSuggestion suggestion) {
            this.suggestion = suggestion;
            this.timestamp = System.currentTimeMillis();
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_VALIDITY;
        }
    }
    
    /**
     * Rule statistics
     */
    public static class RuleStatistics {
        public int totalRules;
        public int activeRules;
        public Map<String, Integer> rulesByCategory;
    }
}

/**
 * Base class for automation rules
 */
abstract class AutomationRule {
    public final String ruleId;
    public String description;
    public String category;
    public boolean enabled;
    public int priority;
    
    public AutomationRule(String ruleId) {
        this.ruleId = ruleId;
        this.enabled = true;
        this.priority = 0;
        this.category = "general";
    }
    
    /**
     * Evaluate rule against current context
     * @param context Current fused context
     * @return Automation suggestion or null if rule doesn't apply
     */
    public abstract ProactiveAutomationEngine.AutomationSuggestion evaluate(
        IAiContextEngine.FusedContext context);
}
