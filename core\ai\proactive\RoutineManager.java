package com.jarvis.core.ai.proactive;

import android.util.Log;
import com.jarvis.core.ai.interfaces.IAiContextEngine;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Routine Manager
 * Manages user routines and automated routine execution
 */
public class RoutineManager {
    
    private static final String TAG = "RoutineManager";
    private static final float MIN_ROUTINE_CONFIDENCE = 0.6f;
    
    // User-defined routines
    private final Map<String, UserRoutine> userRoutines = new ConcurrentHashMap<>();
    
    // Learned routines from user behavior
    private final Map<String, LearnedRoutine> learnedRoutines = new ConcurrentHashMap<>();
    
    // Routine execution history
    private final List<RoutineExecution> executionHistory = new ArrayList<>();
    
    public RoutineManager() {
        initializeDefaultRoutines();
        Log.d(TAG, "Routine Manager initialized");
    }
    
    /**
     * Generate routine-based automation suggestions
     */
    public List<ProactiveAutomationEngine.AutomationSuggestion> generateSuggestions(
            IAiContextEngine.FusedContext context) {
        
        List<ProactiveAutomationEngine.AutomationSuggestion> suggestions = new ArrayList<>();
        
        // Check user-defined routines
        suggestions.addAll(checkUserRoutines(context));
        
        // Check learned routines
        suggestions.addAll(checkLearnedRoutines(context));
        
        return suggestions;
    }
    
    /**
     * Add user-defined routine
     */
    public void addUserRoutine(UserRoutine routine) {
        userRoutines.put(routine.routineId, routine);
        Log.d(TAG, "Added user routine: " + routine.name);
    }
    
    /**
     * Remove user routine
     */
    public void removeUserRoutine(String routineId) {
        UserRoutine removed = userRoutines.remove(routineId);
        if (removed != null) {
            Log.d(TAG, "Removed user routine: " + removed.name);
        }
    }
    
    /**
     * Learn routine from user behavior
     */
    public void learnRoutineFromBehavior(List<String> actionSequence, 
                                       Map<String, Object> triggerContext) {
        
        if (actionSequence.size() < 2) return; // Need at least 2 actions for a routine
        
        String routineKey = generateRoutineKey(actionSequence, triggerContext);
        
        LearnedRoutine routine = learnedRoutines.computeIfAbsent(
            routineKey, k -> new LearnedRoutine(k));
        
        routine.addOccurrence(actionSequence, triggerContext);
        
        Log.d(TAG, "Learned routine pattern: " + routineKey);
    }
    
    /**
     * Get routine statistics
     */
    public RoutineStatistics getRoutineStatistics() {
        RoutineStatistics stats = new RoutineStatistics();
        
        stats.totalUserRoutines = userRoutines.size();
        stats.totalLearnedRoutines = learnedRoutines.size();
        stats.activeRoutines = (int) userRoutines.values().stream()
            .filter(routine -> routine.enabled)
            .count();
        
        stats.executionHistory = executionHistory.size();
        stats.successfulExecutions = (int) executionHistory.stream()
            .filter(execution -> execution.success)
            .count();
        
        return stats;
    }
    
    /**
     * Check user-defined routines for triggers
     */
    private List<ProactiveAutomationEngine.AutomationSuggestion> checkUserRoutines(
            IAiContextEngine.FusedContext context) {
        
        List<ProactiveAutomationEngine.AutomationSuggestion> suggestions = new ArrayList<>();
        
        for (UserRoutine routine : userRoutines.values()) {
            if (!routine.enabled) continue;
            
            if (routine.isTriggered(context)) {
                ProactiveAutomationEngine.AutomationSuggestion suggestion = 
                    new ProactiveAutomationEngine.AutomationSuggestion(
                        "routine_" + routine.routineId + "_" + System.currentTimeMillis(),
                        "Execute routine: " + routine.name,
                        ProactiveAutomationEngine.AutomationType.ROUTINE
                    );
                
                suggestion.confidence = routine.confidence;
                suggestion.autoExecute = routine.autoExecute;
                suggestion.actions.addAll(routine.actions);
                suggestion.parameters.putAll(routine.parameters);
                
                suggestions.add(suggestion);
            }
        }
        
        return suggestions;
    }
    
    /**
     * Check learned routines for patterns
     */
    private List<ProactiveAutomationEngine.AutomationSuggestion> checkLearnedRoutines(
            IAiContextEngine.FusedContext context) {
        
        List<ProactiveAutomationEngine.AutomationSuggestion> suggestions = new ArrayList<>();
        
        for (LearnedRoutine routine : learnedRoutines.values()) {
            if (routine.confidence < MIN_ROUTINE_CONFIDENCE) continue;
            
            if (routine.matchesContext(context)) {
                ProactiveAutomationEngine.AutomationSuggestion suggestion = 
                    new ProactiveAutomationEngine.AutomationSuggestion(
                        "learned_routine_" + routine.routineId + "_" + System.currentTimeMillis(),
                        "Continue your usual routine",
                        ProactiveAutomationEngine.AutomationType.ROUTINE
                    );
                
                suggestion.confidence = routine.confidence;
                suggestion.autoExecute = false; // Learned routines require confirmation
                suggestion.actions.addAll(routine.actionSequence);
                
                suggestions.add(suggestion);
            }
        }
        
        return suggestions;
    }
    
    /**
     * Initialize default routines
     */
    private void initializeDefaultRoutines() {
        // Morning routine
        UserRoutine morningRoutine = new UserRoutine("morning_default", "Morning Routine");
        morningRoutine.addTimeTrigger(7, 0, 9, 0); // 7 AM to 9 AM
        morningRoutine.actions.add("Check calendar for today");
        morningRoutine.actions.add("Review weather forecast");
        morningRoutine.actions.add("Show important notifications");
        morningRoutine.confidence = 0.8f;
        morningRoutine.autoExecute = false;
        userRoutines.put(morningRoutine.routineId, morningRoutine);
        
        // Evening routine
        UserRoutine eveningRoutine = new UserRoutine("evening_default", "Evening Routine");
        eveningRoutine.addTimeTrigger(18, 0, 22, 0); // 6 PM to 10 PM
        eveningRoutine.actions.add("Dim screen brightness");
        eveningRoutine.actions.add("Enable night mode");
        eveningRoutine.actions.add("Review tomorrow's schedule");
        eveningRoutine.confidence = 0.7f;
        eveningRoutine.autoExecute = false;
        userRoutines.put(eveningRoutine.routineId, eveningRoutine);
        
        // Work start routine
        UserRoutine workRoutine = new UserRoutine("work_start", "Start Work Day");
        workRoutine.addTimeTrigger(8, 30, 9, 30); // 8:30 AM to 9:30 AM
        workRoutine.addDayOfWeekTrigger(Calendar.MONDAY, Calendar.FRIDAY);
        workRoutine.actions.add("Open productivity apps");
        workRoutine.actions.add("Enable focus mode");
        workRoutine.actions.add("Check work calendar");
        workRoutine.confidence = 0.9f;
        workRoutine.autoExecute = false;
        userRoutines.put(workRoutine.routineId, workRoutine);
    }
    
    /**
     * Generate routine key from action sequence and context
     */
    private String generateRoutineKey(List<String> actionSequence, 
                                    Map<String, Object> triggerContext) {
        
        StringBuilder key = new StringBuilder();
        
        // Add time component
        Calendar cal = Calendar.getInstance();
        key.append("h").append(cal.get(Calendar.HOUR_OF_DAY)).append("_");
        
        // Add day component
        key.append("d").append(cal.get(Calendar.DAY_OF_WEEK)).append("_");
        
        // Add action sequence hash
        key.append("seq").append(actionSequence.hashCode()).append("_");
        
        // Add context hash
        key.append("ctx").append(triggerContext.hashCode());
        
        return key.toString();
    }
    
    /**
     * User-defined routine
     */
    public static class UserRoutine {
        public final String routineId;
        public String name;
        public String description;
        public boolean enabled;
        public float confidence;
        public boolean autoExecute;
        
        public final List<String> actions;
        public final Map<String, Object> parameters;
        public final List<TimeTrigger> timeTriggers;
        public final List<ContextTrigger> contextTriggers;
        public final Set<Integer> daysOfWeek;
        
        public UserRoutine(String routineId, String name) {
            this.routineId = routineId;
            this.name = name;
            this.enabled = true;
            this.confidence = 1.0f;
            this.autoExecute = false;
            
            this.actions = new ArrayList<>();
            this.parameters = new HashMap<>();
            this.timeTriggers = new ArrayList<>();
            this.contextTriggers = new ArrayList<>();
            this.daysOfWeek = new HashSet<>();
        }
        
        public void addTimeTrigger(int startHour, int startMinute, int endHour, int endMinute) {
            timeTriggers.add(new TimeTrigger(startHour, startMinute, endHour, endMinute));
        }
        
        public void addContextTrigger(IAiContextEngine.ContextType contextType, String key, Object value) {
            contextTriggers.add(new ContextTrigger(contextType, key, value));
        }
        
        public void addDayOfWeekTrigger(int... days) {
            for (int day : days) {
                daysOfWeek.add(day);
            }
        }
        
        public boolean isTriggered(IAiContextEngine.FusedContext context) {
            // Check day of week
            if (!daysOfWeek.isEmpty()) {
                Calendar cal = Calendar.getInstance();
                if (!daysOfWeek.contains(cal.get(Calendar.DAY_OF_WEEK))) {
                    return false;
                }
            }
            
            // Check time triggers
            if (!timeTriggers.isEmpty()) {
                boolean timeMatched = false;
                Calendar cal = Calendar.getInstance();
                int currentHour = cal.get(Calendar.HOUR_OF_DAY);
                int currentMinute = cal.get(Calendar.MINUTE);
                
                for (TimeTrigger trigger : timeTriggers) {
                    if (trigger.isTriggered(currentHour, currentMinute)) {
                        timeMatched = true;
                        break;
                    }
                }
                
                if (!timeMatched) return false;
            }
            
            // Check context triggers
            for (ContextTrigger trigger : contextTriggers) {
                if (!trigger.isTriggered(context)) {
                    return false;
                }
            }
            
            return true;
        }
    }
    
    /**
     * Learned routine from user behavior
     */
    private static class LearnedRoutine {
        public final String routineId;
        public final List<String> actionSequence;
        public final Map<String, Object> triggerContext;
        public float confidence;
        public int occurrences;
        
        public LearnedRoutine(String routineId) {
            this.routineId = routineId;
            this.actionSequence = new ArrayList<>();
            this.triggerContext = new HashMap<>();
            this.confidence = 0.0f;
            this.occurrences = 0;
        }
        
        public void addOccurrence(List<String> actions, Map<String, Object> context) {
            if (actionSequence.isEmpty()) {
                actionSequence.addAll(actions);
                triggerContext.putAll(context);
            }
            
            occurrences++;
            confidence = Math.min(occurrences / 5.0f, 1.0f); // Max confidence after 5 occurrences
        }
        
        public boolean matchesContext(IAiContextEngine.FusedContext context) {
            // Simple context matching based on time and activity
            Calendar cal = Calendar.getInstance();
            int currentHour = cal.get(Calendar.HOUR_OF_DAY);
            
            Integer triggerHour = (Integer) triggerContext.get("hour");
            if (triggerHour != null) {
                return Math.abs(currentHour - triggerHour) <= 1; // Within 1 hour
            }
            
            return false;
        }
    }
    
    /**
     * Time trigger for routines
     */
    private static class TimeTrigger {
        public final int startHour;
        public final int startMinute;
        public final int endHour;
        public final int endMinute;
        
        public TimeTrigger(int startHour, int startMinute, int endHour, int endMinute) {
            this.startHour = startHour;
            this.startMinute = startMinute;
            this.endHour = endHour;
            this.endMinute = endMinute;
        }
        
        public boolean isTriggered(int currentHour, int currentMinute) {
            int currentTime = currentHour * 60 + currentMinute;
            int startTime = startHour * 60 + startMinute;
            int endTime = endHour * 60 + endMinute;
            
            return currentTime >= startTime && currentTime <= endTime;
        }
    }
    
    /**
     * Context trigger for routines
     */
    private static class ContextTrigger {
        public final IAiContextEngine.ContextType contextType;
        public final String key;
        public final Object value;
        
        public ContextTrigger(IAiContextEngine.ContextType contextType, String key, Object value) {
            this.contextType = contextType;
            this.key = key;
            this.value = value;
        }
        
        public boolean isTriggered(IAiContextEngine.FusedContext context) {
            if (!context.contexts.containsKey(contextType)) {
                return false;
            }
            
            IAiContextEngine.ContextData contextData = context.contexts.get(contextType);
            Object contextValue = contextData.data.get(key);
            
            return Objects.equals(value, contextValue);
        }
    }
    
    /**
     * Routine execution record
     */
    private static class RoutineExecution {
        public String routineId;
        public long timestamp;
        public boolean success;
        public String result;
    }
    
    /**
     * Routine statistics
     */
    public static class RoutineStatistics {
        public int totalUserRoutines;
        public int totalLearnedRoutines;
        public int activeRoutines;
        public int executionHistory;
        public int successfulExecutions;
    }
}
